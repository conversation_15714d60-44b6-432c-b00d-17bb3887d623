pageextension 60003 "Warehouse Shipment IPK" extends "Warehouse Shipment"
{
    layout
    {
        // modify("No.")
        // {
        //     //Editable = false;
        // }
        addafter("No.")
        {
            field("Sell-to Source No. INF IPK"; Rec."Sell-to Source No. INF")
            {
                ApplicationArea = All;
                ShowMandatory = true;
                ToolTip = 'Specifies the value of the Sell-to Source No. field.';
            }
            field("Sell-to Customer Name INF IPK"; Rec."Sell-to Customer Name INF")
            {
                ApplicationArea = All;
                Editable = false;
#pragma warning disable LC0066
                ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
#pragma warning restore LC0066
            }
            field("Load Count IPK"; Rec."Load Count IPK")
            {
                ApplicationArea = All;
            }
        }

        addlast(General)
        {

            field("Single Shipment Per Order IPK"; Rec."Single Shipment Per Order IPK")
            {
                ApplicationArea = All;
            }
        }
        addafter(Status)
        {
            group("Export IPK")
            {
                Caption = 'Export';
                Visible = true;// Rec."Is Export IPK";
                field("Cutt-off Date IPK"; Rec."Cutt-off Date IPK")
                {
                    ApplicationArea = All;
                }
                field("ETA IPK"; Rec."ETA IPK")
                {
                    ApplicationArea = All;
                }
                field("ETS IPK"; Rec."ETS IPK")
                {
                    ApplicationArea = All;
                }
                field("Forwarder IPK"; Rec."Forwarder IPK")
                {
                    ApplicationArea = All;
                }
                field("Line IPK"; Rec."Line IPK")
                {
                    ApplicationArea = All;
                }
                field("PoA IPK"; Rec."PoA Code IPK")
                {
                    ApplicationArea = All;
                }
                field("PoA Description IPK"; Rec."PoA Description IPK")
                {
                    ApplicationArea = All;
                }
                field("PoL IPK"; Rec."PoL Code IPK")
                {
                    ApplicationArea = All;
                }
                field("PoL Description IPK"; Rec."PoL Description IPK")
                {
                    ApplicationArea = All;
                }
                field("Vessel Name IPK"; Rec."Vessel Name IPK")
                {
                    ApplicationArea = All;
                }
                field("Voyage No. IPK"; Rec."Voyage No. IPK")
                {
                    ApplicationArea = All;
                }
                field("Priority Shipment IPK"; Rec."Priority Shipment IPK")
                {
                    ApplicationArea = All;
                }
                field("Salesperson Code IPK"; Rec."Salesperson Code IPK")
                {
                    ApplicationArea = All;
                }
                field("Export No. IPK"; Rec."Export No. IPK")
                {
                    ApplicationArea = All;
                }
                field("Total Net Weight IPK"; Rec."Total Net Weight IPK")
                {
                    ApplicationArea = All;
                }
                field("Total Gross Weight IPK"; Rec."Total Gross Weight IPK")
                {
                    ApplicationArea = All;
                }
                field("Total Volume IPK"; Rec."Total Volume IPK")
                {
                    ApplicationArea = All;
                }
                field("Total Unit Quantity IPK"; Rec."Total Unit Quantity IPK")
                {
                    ApplicationArea = All;
                }
                field("Customs Officer IPK"; Rec."Customs Officer IPK")
                {
                    ApplicationArea = All;
                }
                field("Customs IPK"; Rec."Customs Code IPK")
                {
                    ApplicationArea = All;
                }
                field("Customs Description IPK"; Rec."Customs Description IPK")
                {
                    ApplicationArea = All;
                }
                field("Flag IPK"; Rec."Flag IPK")
                {
                    ApplicationArea = All;
                }
            }
        }
        addafter("Vessel Name IPK")
        {
            field("Vessel Shipping Agent Code IPK"; Rec."Vessel Shipping Agent Code IPK")
            {
                ApplicationArea = Warehouse;
            }
        }
        modify("Transport Means INF")
        {
            ShowMandatory = true;
        }
        modify("Zone Code")
        {
            Visible = false;
        }
        modify("Bin Code")
        {
            Visible = false;
        }
        modify("Assigned User ID")
        {
            Visible = false;
        }
        modify("Assignment Date")
        {
            Visible = false;
        }
        modify("Assignment Time")
        {
            Visible = false;
        }
        modify("Sorting Method")
        {
            Visible = false;
        }

        moveafter("Load Count IPK"; "Location Code")
        moveafter("Load Count IPK"; "Posting Date")
        moveafter("Location Code"; "External Document No.")
        movebefore("Cutt-off Date IPK"; "Exit Point INF")
        moveafter(Status; "Transport Means INF")
    }
    actions
    {
        addafter("Get Source Documents")
        {
            action("PrepareForShipment IPK")
            {
                ApplicationArea = All;
                Caption = 'Prepare for Loading', Comment = 'TRK="Yükleme için Hazırla"';
                Image = AddAction;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                // Enabled = not Rec."Is Export IPK";
                ToolTip = 'Executes the Prepare for Loading action.';

                trigger OnAction()
                begin
                    IpekSalesManagement.PrepareForSingleShipment(Rec);
                end;
            }
        }
        modify("Create Pick")
        {
            Visible = false;
        }
        modify("Post and &Print")
        {
            Visible = false;
        }
        modify("Autofill Qty. to Ship")
        {
            Visible = false;
        }
        modify("Use Filters to Get Src. Docs.")
        {
            Visible = false;
        }
        modify("Pick Lines")
        {
            Visible = false;
        }
        modify("Registered P&ick Lines")
        {
            Visible = false;
        }
        modify("Delete Qty. to Ship")
        {
            Visible = false;
        }
    }
    var
        IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
}