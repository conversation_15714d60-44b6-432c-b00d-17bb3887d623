report 60007 "Single Palette Label IPK"
{

    ApplicationArea = All;
    Caption = 'Single Palette Label';
    UsageCategory = ReportsAndAnalysis;
    DefaultLayout = RDLC;
    RDLCLayout = 'src/reportlayout/SinglePaletteLabelIPK.Report.rdlc';
    dataset
    {
        dataitem(PackageNoInformation; "Package No. Information")
        {
            column(PackageNo; "Package No.")
            {
            }
            column(LocationCodeIPK; "Location Code IPK")
            {
            }
            column(PackageNoBarCode; PackageNoBarCode)
            {
            }
            column(PackageNoQRCode; PackageNoQRCode)
            {
            }
            column(CreatedAtIPK_PackageNoInformation; "Created At IPK")
            {
            }
            column(DocumentNoIPK_PackageNoInformation; "Document No. IPK")
            {
            }
            column(TotalQuantity_PackageNoInformation; "Total Quantity IPK")
            {
            }
            column(Unit; 'KG')
            {
            }

            trigger OnAfterGetRecord()
            var
                BarcodeFontProvider: Interface "Barcode Font Provider";
                BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
                BarcodeString: Text;

            begin

                BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;

                // Item.SetLoadFields(Item.Description);
                // Item.Get("Item No.");
                // Description := Item.Description;


                if "Package No." <> '' then begin
                    BarcodeString := "Package No.";

                    BarcodeFontProvider.ValidateInput(BarcodeString, BarcodeSymbology);

                    PackageNoBarCode := BarcodeFontProvider.EncodeFont(BarcodeString, BarcodeSymbology);
                    PackageNoQRCode := BarcodeFontProvider2D.EncodeFont(BarcodeString, BarcodeSymbology2D);
                end
            end;
        }
    }
    trigger OnInitReport()
    begin
        BarcodeSymbology := Enum::"Barcode Symbology"::Code128;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
    end;

    var
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        PackageNoBarCode: Text;
        PackageNoQRCode: Text;
}