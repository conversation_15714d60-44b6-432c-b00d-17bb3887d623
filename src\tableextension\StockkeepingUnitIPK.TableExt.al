tableextension 60003 "Stockkeeping Unit IPK" extends "Stockkeeping Unit"
{
    fields
    {
        field(60000; "Variant Description IPK"; Text[100])
        {
            Caption = 'Variant Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Variant".Description where("Item No." = field("Item No."), Code = field("Variant Code")));
            ToolTip = 'Specifies the value of the Variant Description field.';
        }
        modify("Variant Code")
        {
            trigger OnAfterValidate()
            begin
                Rec.CalcFields("Variant Description IPK");
            end;
        }
    }
}