tableextension 60008 "Package No. Information IPK" extends "Package No. Information"
{

    fields
    {
        field(60000; "Location Code IPK"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
            TableRelation = Location.Code;
            trigger OnValidate()
            var
                Location: Record Location;
            begin

                Location.Get(Rec."Location Code IPK");
                Location.FindFirst();
                if Location."Production Location IPK" then
                    Rec.Validate(Blocked, true)
                // else
                //     Rec.Validate(Blocked, false);
            end;
        }
        field(60001; "Machine/Line No. IPK"; Code[20])
        {
            Caption = 'Machine/Line No.';
            ToolTip = 'Specifies the value of the Machine/Line No. field.';
        }
        field(60002; "Document No. IPK"; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(60003; "Created At IPK"; DateTime)
        {
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Producted At field.';
            Caption = 'Producted At';
        }
        field(60004; "Sales Order No. IPK"; Code[20])
        {
            Caption = 'Sales Order No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Sales Order No. field.';
        }
        field(60005; "Warehouse Shipment No. IPK"; Code[20])
        {
            Caption = 'Warehouse Shipment No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Warehouse Shipment No. field.';
        }
        field(60006; "Load No. IPK"; Code[20])
        {
            Caption = 'Load No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Load No. field.';
        }
        field(60007; "E-Shipment No. IPK"; Code[20])
        {
            Caption = 'E-Shipment No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the E-Shipment No. field.';
        }
        field(60008; "Line Item Description IPK"; Text[100])
        {
            Caption = 'Line Item Description';
            ToolTip = 'Specifies the value of the Line Item Description field.';
            Editable = false;
            FieldClass = FlowField;
            AllowInCustomizations = Always;
            CalcFormula = lookup("Package No. Info. Line IPK".Description where("Package No." = field("Package No.")));
        }
        field(60009; "Total Quantity IPK"; Decimal)
        {
            Caption = 'Total Quantity';
            ToolTip = 'Specifies the value of the Total Quantity field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Package No. Info. Line IPK".Quantity where("Package No." = field("Package No.")));
        }
        field(60010; "Line Item No. IPK"; Code[20])//hata verebilir kontrol et
        {
            Caption = 'Line Item No.';
            ToolTip = 'Specifies the value of the Line Item No. field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Info. Line IPK"."Line Item No." where("Package No." = field("Package No.")));
        }
        field(60011; "Line Variant Code IPK"; Code[10])
        {
            Caption = 'Line Variant Code';
            ToolTip = 'Specifies the value of the Line Variant Code field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Info. Line IPK"."Line Variant Code" where("Package No." = field("Package No.")));
        }
        field(60012; "New Package No. IPK"; Code[50])
        {
            Caption = 'New Package No.';
            ToolTip = 'Specifies the value of the New Package No. field.';
        }
        field(60013; "Pallet IPK"; Boolean)
        {
            Caption = 'Pallet';
            Editable = false;
            ToolTip = 'Specifies the value of the Pallet field.';

        }
        field(60014; "Last Adjustment IPK"; DateTime)
        {
            Caption = 'Last Adjustment';
            ToolTip = 'Specifies the value of the Last Adjustment field.';
        }
        field(60015; "Flagged For Delete IPK"; Boolean)
        {
            Caption = 'Flagged For Delete';
            ToolTip = 'Specifies the value of the Flagged For Delete field.';
        }
        field(60016; "Produced By IPK"; Enum "Production Centers IPK")
        {
            Caption = 'Produced By';
            ToolTip = 'Specifies the value of the Produced By field.';
            InitValue = New;
        }

    }
    keys
    {
        key(key1; "Location Code IPK", "Pallet IPK")
        {

        }
    }
    fieldgroups
    {
        addlast(DropDown; "Total Quantity IPK")
        { }
    }
    // var
    //     IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";
}