page 60061 "Merge Inventory Adjustment IPK"
{
    Caption = 'Merge Inventory Adjustment';
    PageType = StandardDialog;
    ApplicationArea = all;
    layout
    {
        area(Content)
        {
            field(StartDate; StartDate)
            {
                Caption = 'Start Date';
                ToolTip = 'Specifies the value of the Start Date field.';
            }
            field(EndDate; EndDate)
            {
                Caption = 'Start Date';
                ToolTip = 'Specifies the value of the Start Date field.';
            }
            field(MergeLocationCode; MergeLocationCode)
            {
                Caption = 'Merge Location Code';
                ToolTip = 'Specifies the value of the Merge Document No. field.';
            }
        }
    }

    var
        MergeLocationCode: Code[10];
        EndDate: DateTime;
        StartDate: DateTime;

    procedure GetStartDate(): DateTime
    begin
        exit(StartDate);
    end;

    procedure GetEndDate(): DateTime
    begin
        exit(EndDate);
    end;

    procedure GetMergeLocationCode(): Code[10]
    begin
        exit(MergeLocationCode);
    end;
}