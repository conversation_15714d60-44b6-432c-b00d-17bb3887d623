codeunit 60009 "Inventory Adjustment IPK"
{

    // SingleInstance = true;


    procedure ClearRemainingInventoryAtLocation(InventoryAdjustmentHeader: Record "Inventory Adjustment Header")
    var
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ItemLedgerEntry: Record "Item Ledger Entry";
        ItemJournalLineNo: Integer;
        InventoryAdjustmentItemList: Record "Inventory Adjustment Item List";
    begin
        ItemLedgerEntry.SetRange("Location Code", InventoryAdjustmentHeader."Location Code");

        // If there are items in the item list, filter by them, otherwise process all items at location
        InventoryAdjustmentItemList.SetRange("Document No.", InventoryAdjustmentHeader."No.");
        if InventoryAdjustmentItemList.FindSet() then
            repeat
                ItemLedgerEntry.SetRange("Item No.", InventoryAdjustmentItemList."Item No.");
                ItemLedgerEntry.SetRange(Open, true);
                if ItemLedgerEntry.FindSet() then begin
                    LastItemJournalLine.SetRange("Journal Template Name", InventoryAdjustmentHeader."Journal Template Name");
                    LastItemJournalLine.SetRange("Journal Batch Name", InventoryAdjustmentHeader."Journal Batch Name");
                    if not LastItemJournalLine.FindLast() then
                        ItemJournalLineNo := 10000
                    else
                        ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;
                    repeat
                        ItemJournalLine.Init();
                        ItemJournalLine."Line No." := ItemJournalLineNo;
                        ItemJournalLineNo += 10000;
                        ItemJournalLine."Journal Template Name" := InventoryAdjustmentHeader."Journal Template Name";
                        ItemJournalLine."Journal Batch Name" := InventoryAdjustmentHeader."Journal Batch Name";
                        ItemJournalLine.SetUpNewLine(LastItemJournalLine);
                        ItemJournalLine.Insert(true);
                        ItemJournalLine.Validate("Item No.", ItemLedgerEntry."Item No.");
                        ItemJournalLine.Validate("Variant Code", ItemLedgerEntry."Variant Code");
                        ItemJournalLine.Validate("Location Code", ItemLedgerEntry."Location Code");
                        ItemJournalLine.Validate("Lot No.", ItemLedgerEntry."Lot No.");
                        ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Negative Adjmt.");
                        ItemJournalLine.Validate(Quantity, Abs(ItemLedgerEntry."Remaining Quantity"));
                        ItemJournalLine.Modify(true);
                        LastItemJournalLine := ItemJournalLine;
                    until ItemLedgerEntry.Next() = 0;
                end;
                // Remove filter for next item
                ItemLedgerEntry.SetRange("Item No.");
            until InventoryAdjustmentItemList.Next() = 0
        else begin
            // Fallback: no item list, process all items at location as before
            ItemLedgerEntry.SetRange(Open, true);
            if ItemLedgerEntry.FindSet() then begin
                LastItemJournalLine.SetRange("Journal Template Name", InventoryAdjustmentHeader."Journal Template Name");
                LastItemJournalLine.SetRange("Journal Batch Name", InventoryAdjustmentHeader."Journal Batch Name");
                if not LastItemJournalLine.FindLast() then
                    ItemJournalLineNo := 10000
                else
                    ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;
                repeat
                    ItemJournalLine.Init();
                    ItemJournalLine."Line No." := ItemJournalLineNo;
                    ItemJournalLineNo += 10000;
                    ItemJournalLine."Journal Template Name" := InventoryAdjustmentHeader."Journal Template Name";
                    ItemJournalLine."Journal Batch Name" := InventoryAdjustmentHeader."Journal Batch Name";
                    ItemJournalLine.SetUpNewLine(LastItemJournalLine);
                    ItemJournalLine.Insert(true);
                    ItemJournalLine.Validate("Item No.", ItemLedgerEntry."Item No.");
                    ItemJournalLine.Validate("Variant Code", ItemLedgerEntry."Variant Code");
                    ItemJournalLine.Validate("Location Code", ItemLedgerEntry."Location Code");
                    ItemJournalLine.Validate("Lot No.", ItemLedgerEntry."Lot No.");
                    ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Negative Adjmt.");
                    ItemJournalLine.Validate(Quantity, Abs(ItemLedgerEntry."Remaining Quantity"));
                    ItemJournalLine.Modify(true);
                    LastItemJournalLine := ItemJournalLine;
                until ItemLedgerEntry.Next() = 0;
            end;
        end;
    end;

    procedure CreateInventoryAdjustmentLineDetailFromInventoryAdjustmentLine(InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK")
    var
        InventoryAdjLineDetail: Record "Inventory Adj. Line Detail IPK";
        // InventoryAdjustmentHeader: Record "Inventory Adjustment Header";
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageLineNotFoundErr: Label 'Cannot Find any package line.';
    // RemainingQuantity: Decimal;
    // ItemJournalLineNo: Integer;
    begin
        PackageNoInfoLine.CalcFields("Location Code");
        PackageNoInfoLine.SetRange("Item No.", '');
        PackageNoInfoLine.SetRange("Variant Code", '');
        PackageNoInfoLine.SetRange("Package No.", InventoryAdjustmentLine."Package No.");
        PackageNoInfoLine.SetFilter("Lot No.", '<>%1', '');
        if not PackageNoInfoLine.FindSet(false) then
            Error(PackageLineNotFoundErr);

        repeat

            InventoryAdjLineDetail.Init();
            InventoryAdjLineDetail."Document No." := InventoryAdjustmentLine."Document No.";
            InventoryAdjLineDetail."Document Line No." := InventoryAdjustmentLine."Line No.";
            InventoryAdjLineDetail.Insert(true);
            InventoryAdjLineDetail.Validate("Item No.", PackageNoInfoLine."Line Item No.");
            InventoryAdjLineDetail.Validate("Variant Code", PackageNoInfoLine."Line Variant Code");
            InventoryAdjLineDetail.Validate("Location Code", PackageNoInfoLine."Location Code");
            InventoryAdjLineDetail.Validate("Lot No.", PackageNoInfoLine."Lot No.");
            InventoryAdjLineDetail.Validate("Line Physical Quantity", PackageNoInfoLine.Quantity);
            InventoryAdjLineDetail.Validate("Line Quantity", PackageNoInfoLine.Quantity);
            InventoryAdjLineDetail.Modify(true);

        until PackageNoInfoLine.Next() = 0;
    end;



    procedure CreateItemJournalLineDetailFromInventoryAdjustmentLine(InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK"; Qty: Decimal; LotNo: Code[50])
    var
        InventoryAdjustmentHeader: Record "Inventory Adjustment Header";
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ItemJournalLineNo: Integer;
    begin

        InventoryAdjustmentHeader.Get(InventoryAdjustmentLine."Document No.");

        LastItemJournalLine.SetRange("Journal Template Name", InventoryAdjustmentHeader."Journal Template Name");
        LastItemJournalLine.SetRange("Journal Batch Name", InventoryAdjustmentHeader."Journal Batch Name");

        if not LastItemJournalLine.FindLast() then
            ItemJournalLineNo := 10000
        else
            ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;

        ItemJournalLine.SetRange("Item No.", InventoryAdjustmentLine."Line Item No.");
        ItemJournalLine.SetRange("Variant Code", InventoryAdjustmentLine."Line Variant Code");
        ItemJournalLine.SetRange("Journal Template Name", InventoryAdjustmentHeader."Journal Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", InventoryAdjustmentHeader."Journal Batch Name");
        ItemJournalLine.SetRange("Location Code", InventoryAdjustmentHeader."Location Code");
        ItemJournalLine.SetRange("Lot No.", LotNo);
        ItemJournalLine.SetRange("Palette Barcode IPK", InventoryAdjustmentLine."Package No.");
        if not ItemJournalLine.FindFirst() then begin
            ItemJournalLine.Init();
            ItemJournalLine."Line No." := ItemJournalLineNo;
            ItemJournalLine."Journal Template Name" := InventoryAdjustmentHeader."Journal Template Name";
            ItemJournalLine."Journal Batch Name" := InventoryAdjustmentHeader."Journal Batch Name";

            ItemJournalLine.SetUpNewLine(LastItemJournalLine);

            ItemJournalLine.Insert(true);
            // ItemJournalLine.Validate("Palette Barcode IPK", InventoryAdjustmentLine."Package No.");
            ItemJournalLine.Validate("Item No.", InventoryAdjustmentLine."Line Item No.");
            ItemJournalLine.Validate("Variant Code", InventoryAdjustmentLine."Line Variant Code");
            ItemJournalLine.Validate("Location Code", InventoryAdjustmentLine."Location Code");
            ItemJournalLine.Validate("Lot No.", LotNo);
            if Qty > 0 then
                ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Positive Adjmt.");
            if Qty < 0 then
                ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Negative Adjmt.");
            ItemJournalLine.Validate(Quantity, Abs(Qty));
            ItemJournalLine.Modify(true);
        end;

    end;

    procedure CreateItemJournalLineDetailFromLotAdjustmentLine(InventoryAdjustmentHeader: Record "Inventory Adjustment Header"; LotAdjustmentLine: Record "Lot Adjustment Line IPK"; Qty: Decimal)
    var
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ItemJournalLineNo: Integer;
    begin


        LastItemJournalLine.SetRange("Journal Template Name", InventoryAdjustmentHeader."Journal Template Name");
        LastItemJournalLine.SetRange("Journal Batch Name", InventoryAdjustmentHeader."Journal Batch Name");

        if not LastItemJournalLine.FindLast() then
            ItemJournalLineNo := 10000
        else
            ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;

        ItemJournalLine.SetRange("Item No.", LotAdjustmentLine."Item No.");
        ItemJournalLine.SetRange("Variant Code", LotAdjustmentLine."Variant Code");
        ItemJournalLine.SetRange("Journal Template Name", InventoryAdjustmentHeader."Journal Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", InventoryAdjustmentHeader."Journal Batch Name");
        ItemJournalLine.SetRange("Location Code", InventoryAdjustmentHeader."Location Code");
        ItemJournalLine.SetRange("Lot No.", LotAdjustmentLine."Lot No.");
        if not ItemJournalLine.FindFirst() then begin
            ItemJournalLine.Init();
            ItemJournalLine."Line No." := ItemJournalLineNo;
            ItemJournalLine."Journal Template Name" := InventoryAdjustmentHeader."Journal Template Name";
            ItemJournalLine."Journal Batch Name" := InventoryAdjustmentHeader."Journal Batch Name";

            ItemJournalLine.SetUpNewLine(LastItemJournalLine);

            ItemJournalLine.Insert(true);
            ItemJournalLine.Validate("Item No.", LotAdjustmentLine."Item No.");
            ItemJournalLine.Validate("Variant Code", LotAdjustmentLine."Variant Code");
            ItemJournalLine.Validate("Location Code", InventoryAdjustmentHeader."Location Code");
            ItemJournalLine.Validate("Lot No.", LotAdjustmentLine."Lot No.");
            if Qty < 0 then
                ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Positive Adjmt.");
            if Qty > 0 then
                ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Negative Adjmt.");
            ItemJournalLine.Validate(Quantity, Abs(Qty));
            ItemJournalLine.Modify(true);
        end;

    end;


    procedure AdjustPackageLineQuantities(ItemJournalLine: Record "Item Journal Line")
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageNoInformation: Record "Package No. Information";
    begin
        PackageNoInfoLine.SetRange("Package No.", ItemJournalLine."Palette Barcode IPK");
        PackageNoInfoLine.SetRange("Lot No.", ItemJournalLine."Lot No.");
        PackageNoInfoLine.FindFirst();
        if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::"Positive Adjmt." then
            PackageNoInfoLine.Quantity += ItemJournalLine.Quantity
        else
            if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::"Negative Adjmt." then
                PackageNoInfoLine.Quantity -= ItemJournalLine.Quantity;
        PackageNoInfoLine."Last Adjustment" := CurrentDateTime();
        PackageNoInfoLine.Modify(true);

        PackageNoInformation.Get('', '', PackageNoInfoLine."Package No.");
        PackageNoInformation."Last Adjustment IPK" := CurrentDateTime();
        PackageNoInformation.Modify(true);
    end;

    procedure PostInventoryAdjustmentLines(var InventoryAdjustmentHeader: Record "Inventory Adjustment Header")
    var
        ItemJournalLine: Record "Item Journal Line";
    begin
        ItemJournalLine.SetRange("Journal Template Name", InventoryAdjustmentHeader."Journal Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", InventoryAdjustmentHeader."Journal Batch Name");
        if ItemJournalLine.FindSet() then begin
            Message('%1 lines of adjustments posted', ItemJournalLine.Count());
            Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);
        end;
        InventoryAdjustmentHeader.Posted := true;
        InventoryAdjustmentHeader.Modify(true);
    end;

    procedure FixPackageQuantities(var InventoryAdjustmentHeader: Record "Inventory Adjustment Header")
    var
        InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
    begin

        InventoryAdjustmentLine.SetRange("Document No.", InventoryAdjustmentHeader."No.");
        InventoryAdjustmentLine.FindSet(true);

        repeat

            AssignQuantityDifferenceFromnventoryAdjusmentLine(InventoryAdjustmentLine);
        until InventoryAdjustmentLine.Next() = 0;
    end;

    procedure AssignQuantityDifferenceFromnventoryAdjusmentLine(var InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK")
    begin

        InventoryAdjustmentLine.CalcFields("Total Quantity");
        InventoryAdjustmentLine.Validate("Quantity Difference", InventoryAdjustmentLine."Physical Quantity" - InventoryAdjustmentLine."Total Quantity");

        InventoryAdjustmentLine.Modify(true);
        AdjustPackageQuantites(InventoryAdjustmentLine, InventoryAdjustmentLine."Quantity Difference");

    end;

    procedure AdjustPackageQuantites(InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK"; var QuantityDifference: Decimal)
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        ReduceQty: Decimal;
    begin

        InventoryAdjustmentLine.CalcFields("Line Item No.", "Line Variant Code");
        PackageNoInfoLine.SetAutoCalcFields("Total Qty. on Location");
        PackageNoInfoLine.SetRange("Package No.", InventoryAdjustmentLine."Package No.");
        PackageNoInfoLine.SetRange("Line Item No.", InventoryAdjustmentLine."Line Item No.");
        PackageNoInfoLine.SetRange("Line Variant Code", InventoryAdjustmentLine."Line Variant Code");

        if PackageNoInfoLine.FindFirst() then
            CreateItemJournalLineDetailFromInventoryAdjustmentLine(InventoryAdjustmentLine, InventoryAdjustmentLine."Physical Quantity", PackageNoInfoLine."Lot No.");
        if QuantityDifference > 0 then begin
            //CreateItemJournalLineDetailFromInventoryAdjustmentLine(InventoryAdjustmentLine, InventoryAdjustmentLine."Physical Quantity" /*QuantityDifference*/, PackageNoInfoLine."Lot No.");
            PackageNoInfoLine.Quantity := PackageNoInfoLine.Quantity + QuantityDifference;
            PackageNoInfoLine.Modify(true);
            QuantityDifference := 0;
        end
        else
            if QuantityDifference < 0 then
                if PackageNoInfoLine.FindSet() then
                    repeat
                        if PackageNoInfoLine.Quantity > 0 then begin
                            if Abs(QuantityDifference) < PackageNoInfoLine.Quantity then
                                ReduceQty := Abs(QuantityDifference)
                            else
                                ReduceQty := PackageNoInfoLine.Quantity;
                            PackageNoInfoLine.Quantity := PackageNoInfoLine.Quantity - ReduceQty;
                            PackageNoInfoLine.Modify(true);
                            QuantityDifference := QuantityDifference + ReduceQty;
                            if QuantityDifference = 0 then
                                break;
                        end;
                    until PackageNoInfoLine.Next() = 0;

    end;

    procedure MergeAdjustmentLists(TargetInventoryAdjustmentHeaderNo: Code[20]; MergeInventoryAdjustmentHeaderNo: Code[20])
    var
        InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
    begin
        InventoryAdjustmentLine.SetRange("Document No.", MergeInventoryAdjustmentHeaderNo);
        InventoryAdjustmentLine.FindSet();
        repeat
            InventoryAdjustmentLine."Document No." := TargetInventoryAdjustmentHeaderNo;
            InventoryAdjustmentLine."Line No." += 100;
        until InventoryAdjustmentLine.Next() = 0;

    end;

    procedure FetchUnreadPackages(InventoryAdjustmentHeader: Record "Inventory Adjustment Header")
    var
        InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
        PackageNoInformation: Record "Package No. Information";
    begin
        InventoryAdjustmentLine.SetRange("Document No.", InventoryAdjustmentHeader."No.");
        // InventoryAdjustmentLine.FindSet();


        PackageNoInformation.SetRange("Location Code IPK", InventoryAdjustmentHeader."Location Code");
        PackageNoInformation.SetRange("Pallet IPK", false);
        PackageNoInformation.FindSet(false);
        repeat

            InventoryAdjustmentLine.SetRange("Package No.", PackageNoInformation."Package No.");
            if InventoryAdjustmentLine.IsEmpty() then begin
                InventoryAdjustmentHeader.Validate(Barcode, PackageNoInformation."Package No.");
                InventoryAdjustmentHeader.Validate("LB Total Phy. Qty.", 0);
            end;

        until PackageNoInformation.Next() = 0;
    end;

    procedure CreateLotAdjustmentLinesCurrentPackageLines(LocationCode: Code[10]; InventoryAdjustmentHeaderDocumentNo: Code[20])
    var
        InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
        LotAdjustmentLine: Record "Lot Adjustment Line IPK";
        EndDateTime: DateTime;
        StartDateTime: DateTime;
    begin

        StartDateTime := CurrentDateTime();
        InventoryAdjustmentLine.SetRange("Document No.", InventoryAdjustmentHeaderDocumentNo);
        InventoryAdjustmentLine.FindSet();

        LotAdjustmentLine.SetRange("Source Document No.", InventoryAdjustmentHeaderDocumentNo);
        if LotAdjustmentLine.FindFirst() then
            LotAdjustmentLine.DeleteAll(true);
        repeat

            CreateLotAdjustmentsFromPackageLines(LocationCode, InventoryAdjustmentLine."Package No.");
        until InventoryAdjustmentLine.Next() = 0;

        EndDateTime := CurrentDateTime();
        Message('Start %1 end %2', StartDateTime, EndDateTime);
    end;

    procedure CreateJournalLinesFromLotAdjustmentLine(InventoryAdjustmentHeader: Record "Inventory Adjustment Header")
    var
        LotAdjustmentLine: Record "Lot Adjustment Line IPK";
        QuantityDifference: Decimal;
    begin
        if LotAdjustmentLine.FindSet() then
            repeat
                QuantityDifference := LotAdjustmentLine."Total Qty. on Location" - LotAdjustmentLine."Physical Quantity";
                if QuantityDifference <> 0 then
                    CreateItemJournalLineDetailFromLotAdjustmentLine(InventoryAdjustmentHeader, LotAdjustmentLine, QuantityDifference);
            until LotAdjustmentLine.Next() = 0;


    end;

    procedure CreateLotAdjustmentsFromPackageLines(LocationCode: Code[10]; PackageNo: Code[50])
    var
        LotAdjustmentLine: Record "Lot Adjustment Line IPK";
        TempLotAdjustmentLine: Record "Lot Adjustment Line IPK" temporary;
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        Window: Dialog;
    begin
        Window.Open('Processing records #1######');

        // Optimize record filtering and field loading
        PackageNoInfoLine.SetAutoCalcFields("Total Qty. on Loc. Pack", "Total Qty. on Location");
        PackageNoInfoLine.SetCurrentKey("Lot No.", "Line Item No.", "Line Variant Code");
        PackageNoInfoLine.SetRange("Location Code", LocationCode);
        PackageNoInfoLine.SetRange("Package No.", PackageNo);
        PackageNoInfoLine.SetRange("Source Package No. IPK", '');
        PackageNoInfoLine.SetLoadFields(
            "Total Qty. on Location",
            "Total Qty. on Loc. Pack",
            "Line Item No.",
            "Line Variant Code",
            "Lot No."
        );

        if PackageNoInfoLine.FindSet(false) then begin
            repeat
                Window.Update(1, PackageNoInfoLine."Line Item No.");

                if not TempLotAdjustmentLine.Get(
                    PackageNoInfoLine."Line Item No.",
                    PackageNoInfoLine."Line Variant Code",
                    PackageNoInfoLine."Lot No."
                ) then begin
                    // Use temporary table for faster processing
                    TempLotAdjustmentLine.Init();
                    TempLotAdjustmentLine."Item No." := PackageNoInfoLine."Line Item No.";
                    TempLotAdjustmentLine."Variant Code" := PackageNoInfoLine."Line Variant Code";
                    TempLotAdjustmentLine."Lot No." := PackageNoInfoLine."Lot No.";
                    TempLotAdjustmentLine."Total Qty. on Location" := PackageNoInfoLine."Total Qty. on Location";
                    TempLotAdjustmentLine."Physical Quantity" := PackageNoInfoLine."Total Qty. on Loc. Pack";
                    TempLotAdjustmentLine.Insert(false);
                end;
            until PackageNoInfoLine.Next() = 0;

            // Batch insert from temporary to real table
            if TempLotAdjustmentLine.FindSet() then
                repeat
                    if not LotAdjustmentLine.Get(
                        TempLotAdjustmentLine."Item No.",
                        TempLotAdjustmentLine."Variant Code",
                        TempLotAdjustmentLine."Lot No."
                    ) then begin
                        LotAdjustmentLine := TempLotAdjustmentLine;
                        LotAdjustmentLine.Insert(true);
                    end;
                until TempLotAdjustmentLine.Next() = 0;
        end;

        Window.Close();
    end;

    procedure TransferPackNoLineToInventoryAdjustment(var PackageNoInfoLine: Record "Package No. Info. Line IPK"; LocationCode: Code[10])
    var
        InventoryAdjustmentHeader: Record "Inventory Adjustment Header";
        InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
    begin
        IpekPamukSetup.GetRecordOnce();
        InventoryAdjustmentHeader.Init();
        // InventoryAdjustmentHeader."No.":=IpekPamukSetup
        PackageNoInfoLine.CalcFields("Location Code");
        InventoryAdjustmentHeader."Location Code" := PackageNoInfoLine."Location Code";
        InventoryAdjustmentHeader."Journal Template Name" := 'MADDGNL';
        InventoryAdjustmentHeader."Journal Batch Name" := 'SAYIM-1';
        InventoryAdjustmentHeader.Insert(true);
        InventoryAdjustmentHeader.Modify(true);
        repeat
            InventoryAdjustmentHeader.Validate(Barcode, PackageNoInfoLine."Package No.");
            InventoryAdjustmentHeader.Modify(true);
        // InventoryAdjustmentLine.Get(PackageNoInfoLine."Package No.");
        // InventoryAdjustmentLine."Physical Quantity" := 0;
        // InventoryAdjustmentLine.Modify(true);
        until PackageNoInfoLine.Next() = 0;
        InventoryAdjustmentLine.SetRange("Document No.", InventoryAdjustmentHeader."No.");
        InventoryAdjustmentLine.ModifyAll("Physical Quantity", 0, true);
        Message('Inventory Adjustment %1 Created', InventoryAdjustmentHeader."No.");
    end;

    procedure TransferPackNoToInventoryAdjustment(var PackageNoInfoLine: Record "Package No. Info. Line IPK")
    var
        InventoryAdjustmentHeader: Record "Inventory Adjustment Header";
        InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
        InventoryAdjustmentLine2: Record "Inventory Adjustment Line IPK";
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
    // PackageNoInformation: Record "Package No. Information";
    // IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
    // PackageNoInfoLine2: Record "Package No. Info. Line IPK";
    begin

        IpekPamukSetup.GetRecordOnce();
        InventoryAdjustmentHeader.Init();
        // InventoryAdjustmentHeader."No.":=IpekPamukSetup

        // PackageNoInfoLine.CalcFields("Location Code");

        InventoryAdjustmentHeader."Location Code" := PackageNoInfoLine."Location Code";
        InventoryAdjustmentHeader."Journal Template Name" := 'MADDGNL';
        InventoryAdjustmentHeader."Journal Batch Name" := 'SAYIM-1';
        InventoryAdjustmentHeader.Insert(true);
        InventoryAdjustmentHeader.Modify(true);
        repeat
            InventoryAdjustmentHeader.Validate(Barcode, PackageNoInfoLine."Package No.");
            InventoryAdjustmentHeader.Modify(true);

            InventoryAdjustmentLine.SetRange("Document No.", InventoryAdjustmentHeader."No.");
            InventoryAdjustmentLine.SetRange("Package No.", PackageNoInfoLine."Package No.");
            InventoryAdjustmentLine.FindFirst();
            // PackageNoInfoLine.CalcFields("Total Qty. on Location");
            if PackageNoInfoLine."Total Qty. on Location" > PackageNoInfoLine.Quantity then
                InventoryAdjustmentLine.Validate("Physical Quantity", 0)
            else begin
                InventoryAdjustmentLine2.SetCurrentKey("Line Item No.", "Line Variant Code", "Location Code", "Line No.");
                InventoryAdjustmentLine2.SetAutoCalcFields("Line Item No.", "Line Variant Code");
                InventoryAdjustmentLine2.SetRange("Document No.", InventoryAdjustmentLine."Document No.");
                InventoryAdjustmentLine2.SetRange("Line Item No.", PackageNoInfoLine."Line Item No.");
                InventoryAdjustmentLine2.SetRange("Line Variant Code", PackageNoInfoLine."Line Variant Code");
                InventoryAdjustmentLine2.SetRange("Location Code", PackageNoInfoLine."Location Code");
                InventoryAdjustmentLine2.SetFilter("Line No.", '<>%1', InventoryAdjustmentLine."Line No.");
                InventoryAdjustmentLine2.FindSet();
                InventoryAdjustmentLine2.CalcSums("Quantity Difference");//Buradan eklenen journal linelar flagged for delete alanı ile doldurulacak
                InventoryAdjustmentLine.Validate("Physical Quantity", PackageNoInfoLine.Quantity - PackageNoInfoLine."Total Qty. on Location" - InventoryAdjustmentLine2."Quantity Difference");//alper

            end;
            InventoryAdjustmentLine.Modify(true);

        until PackageNoInfoLine.Next() = 0;

        // PackageNoInfoLine.ModifyAll("Flagged For Delete", true, true);
        Message('Inventory Adjustment %1 Created', InventoryAdjustmentHeader."No.");


    end;

    procedure AssignLotNoToJrnlLine(var ItemJournalLine: Record "Item Journal Line")
    var
        EntrySummary: Record "Entry Summary";
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        ItemJnlLineReserve: Codeunit "Item Jnl. Line-Reserve";
        ItemTrackingDataCollection: Codeunit "Item Tracking Data Collection";
    begin
        TempSourceTrackingSpecification.DeleteAll(false);
        ItemJnlLineReserve.InitFromItemJnlLine(TempSourceTrackingSpecification, ItemJournalLine);

        ItemTrackingDataCollection.RetrieveLookupData(TempSourceTrackingSpecification, true);

        ItemTrackingDataCollection.GetTempGlobalEntrySummary(EntrySummary);

        EntrySummary.FindSet();
        repeat

            EntrySummary.Validate("Selected Quantity", -(EntrySummary."Total Quantity"));
            EntrySummary.Modify(false);
            ItemTrackingDataCollection.AddSelectedTrackingToDataSet(EntrySummary, TempSourceTrackingSpecification, 1);
        until EntrySummary.Next() = 0;
    end;

    procedure ExistsActiveInventoryAdjustmentForItem(ItemNo: Code[20]): Boolean
    var
        InventoryAdjustmentItemList: Record "Inventory Adjustment Item List";
    begin
        InventoryAdjustmentItemList.CalcFields(Status);
        InventoryAdjustmentItemList.SetRange(Status, InventoryAdjustmentItemList.Status::Active);
        InventoryAdjustmentItemList.SetRange("Item No.", ItemNo);
        exit(not InventoryAdjustmentItemList.IsEmpty());
    end;

    procedure IsItemInAnyActiveInventoryAdjustment(ItemNo: Code[20]): Boolean
    var
        InventoryAdjustmentItemList: Record "Inventory Adjustment Item List";
    begin
        InventoryAdjustmentItemList.CalcFields(Status);
        InventoryAdjustmentItemList.SetRange(Status, InventoryAdjustmentItemList.Status::Active);
        InventoryAdjustmentItemList.SetRange("Item No.", ItemNo);
        exit(not InventoryAdjustmentItemList.IsEmpty());
    end;
}