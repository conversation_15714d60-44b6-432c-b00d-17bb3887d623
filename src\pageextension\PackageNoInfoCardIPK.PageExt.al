pageextension 60006 "Package No. Info. Card IPK" extends "Package No. Information Card"
{

    layout
    {
        modify("Item No.")
        {
            Visible = false;
        }
        modify("Variant Code")
        {
            Visible = false;
        }
        modify(Description)
        {
            Visible = false;
        }
        modify("Country/Region Code")
        {
            Visible = false;
        }
        addlast(General)
        {

            // field("Inventory IPK"; Rec.Inventory)
            // {
            //     ApplicationArea = All;
            //     Visible=false;
            //     ToolTip = 'Specifies the value of the Inventory field.';
            // }
            // field("Lot No. IPK"; Rec."Lot No. IPK")
            // {
            //     ApplicationArea = All;
            //     Visible=false;
            // }

            field("Location Code IPK"; Rec."Location Code IPK")
            {
                ApplicationArea = All;
                TableRelation = Location.Code;
            }
            field("Machine/Line No. IPK"; Rec."Machine/Line No. IPK")
            {
                ApplicationArea = All;
            }
            field("Producted At (DateTime) IPK"; Rec."Created At IPK")
            {
                ApplicationArea = All;
            }
            field("Production Order No. IPK"; Rec."Document No. IPK")
            {
                ApplicationArea = All;
            }
            field("Produced By IPK"; Rec."Produced By IPK")
            {
                ApplicationArea = All;
            }
            field("Warehouse Shipment No. IPK"; Rec."Warehouse Shipment No. IPK")
            {
                ApplicationArea = All;
            }
            field("New Package No. IPK"; Rec."New Package No. IPK")
            {
                ApplicationArea = All;
            }
            field("Last Adjustment IPK"; Rec."Last Adjustment IPK")
            {
                ApplicationArea = All;
                Editable = false;
            }
            field("Pallet IPK"; Rec."Pallet IPK")
            {
                ApplicationArea = All;
            }
            usercontrol(SetFieldFocus; "SetFieldFocus IPK")
            {
                ApplicationArea = all;
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('PackageBarcode IPK');
                end;
            }
            group("Barcode IPK")
            {
                Caption = 'Barcode';
                ShowCaption = false;
                Visible = Rec."Pallet IPK";
                field("PackageBarcode IPK"; Barcode)
                {
                    ToolTip = 'Specifies the value of the Package Barcode field.';
                    Caption = 'Package Barcode';
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        LoadPlanningLine: Record "Load Planning Line IPK";
                        PackageNoInfoLine2: Record "Package No. Info. Line IPK";
                        PackageNoInformation: Record "Package No. Information";
                        PackageAlreadyUsedErr: Label 'This package already used in another pallet';
                        PackItemVariantDifferentErr: Label 'Selected packages Item/Variant is different then the previous packages inside of this palette please check.';
                        PackLocationIsDifferentErr: Label 'Selected packages location is different then the previous packages inside of this palette please check.';
                    begin
                        if Barcode = '' then
                            exit;

                        LoadPlanningLine.SetAutoCalcFields("Quantity Loaded");
                        PackageNoInformation.Get('', '', Barcode);
                        PackageNoInformation.CalcFields("Total Quantity IPK");

                        PackageNoInfoLine2.SetRange("Source Package No. IPK", Barcode);
                        if PackageNoInfoLine2.IsEmpty() then begin
                            PackageNoInfoLine2.Init();
                            PackageNoInfoLine2."Item No." := '';
                            PackageNoInfoLine2."Variant Code" := '';
                            PackageNoInfoLine2."Package No." := Rec."Package No.";
                            //İtem no ve varyant no ilk okutulan madde olarak baz alınacak,
                            //palet içerisindeki bir paket transfer emrinde okutulduğu anda hata vermeli.
                            PackageNoInfoLine2.Insert(true);
                            PackageNoInfoLine2."Source Package No. IPK" := Barcode;
                            PackageNoInfoLine2.Quantity := PackageNoInformation."Total Quantity IPK";
                            if Rec."Location Code IPK" = '' then
                                Rec."Location Code IPK" := PackageNoInformation."Location Code IPK";
                            if Rec."Location Code IPK" <> PackageNoInformation."Location Code IPK" then
                                Error(PackLocationIsDifferentErr);

                            // if (Rec."Line Item No. IPK" = '') and (Rec."Line Variant Code IPK" = '') then begin

                            // end;
                            PackageNoInformation.CalcFields("Line Item No. IPK", "Line Variant Code IPK");
                            PackageNoInfoLine2."Line Item No." := PackageNoInformation."Line Item No. IPK";
                            PackageNoInfoLine2."Line Variant Code" := PackageNoInformation."Line Variant Code IPK";
                            PackageNoInfoLine2.Modify(true);
                            Rec.CalcFields("Line Item No. IPK", "Line Variant Code IPK");
                            if (Rec."Line Item No. IPK" <> PackageNoInformation."Line Item No. IPK") or (Rec."Line Variant Code IPK" <> PackageNoInformation."Line Variant Code IPK") then
                                Error(PackItemVariantDifferentErr);

                        end
                        else
                            Error(PackageAlreadyUsedErr);

                        Barcode := '';
                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('PackageBarcode IPK');
                    end;
                }
            }
        }

        addafter(General)
        {
            part(Lines; "Package No. Info. Subpage IPK")
            {
                Caption = 'Lines';
                ApplicationArea = All;
                SubPageLink = "Package No." = field("Package No.");
                UpdatePropagation = Both;
                Editable = not Rec."Pallet IPK";
            }
        }

    }
    actions
    {
        addafter("&Package")
        {
            action("PrintLabel IPK")
            {
                AccessByPermission = tabledata "Serial No. Information" = I;
                ApplicationArea = ItemTracking;
                Caption = 'Print Label';
                Image = Print;
                ToolTip = 'Executes the Print Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PaletteLabel: Report "Palette Label IPK";
                begin
                    PackageNoInformation := Rec;
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PaletteLabel.SetTableView(PackageNoInformation);
                    PaletteLabel.RunModal();
                end;
            }
            action("Print Palette Label IPK")
            {
                AccessByPermission = tabledata "Serial No. Information" = I;
                ApplicationArea = ItemTracking;
                Caption = 'Print palette Label';
                Image = Print;
                ToolTip = 'Executes the Print palette Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PaletteLabel: Report "Single Palette Label IPK";
                begin
                    PackageNoInformation := Rec;
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PaletteLabel.SetTableView(PackageNoInformation);
                    PaletteLabel.RunModal();
                end;
            }
        }
    }
    var
        Barcode: Code[50];
}