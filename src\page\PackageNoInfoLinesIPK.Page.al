page 60030 "Package No. Info. Lines IPK"
{
    ApplicationArea = All;
    Caption = 'Package No. Info. Lines';
    PageType = List;
    SourceTable = "Package No. Info. Line IPK";
    SourceTableView = sorting(SystemCreatedAt)
                      order(descending);
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                }
                field("Line Item No."; Rec."Line Item No.")
                {
                    ToolTip = 'Specifies the value of the Line Item No. field.';
                }
                field("Line Variant Code"; Rec."Line Variant Code")
                {
                    ToolTip = 'Specifies the value of the Line Variant Code field.';
                    Visible = ShowDetails;
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Total Qty. on Location"; Rec."Total Qty. on Location")
                {
                    Visible = false;
                }
                field("Total Qty. on Loc. Pack"; Rec."Total Qty. on Loc. Pack")
                {
                    ToolTip = 'Specifies the value of the Total Qty. on Loc. Pack" field.';
                    Visible = false;
                }
                field("Location Code"; Rec."Location Code")
                {
                    Visible = ShowDetails;
                }
                field("Production Location"; Rec."Production Location")
                {
                    Visible = false;
                }
                field("Lot No."; Rec."Lot No.")
                {
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                    Visible = false;
                }
                field("Last Adjustment"; Rec."Last Adjustment")
                {
                }
                field("Warehouse Shipment No."; Rec."Warehouse Shipment No.")
                {
                    Visible = ShowDetails;
                    ToolTip = 'Specifies the value of the Total Qty. on Loc. Pack" field.';
                }

                field("Source Package No. IPK"; Rec."Source Package No. IPK")
                {
                    Visible = ShowDetails;
                }
            }
        }
    }
    actions
    {
        area(Reporting)
        {
            action("ShowDetails IPK")
            {
                Caption = 'Show Details', Comment = 'TRK="Detaylaylı Görünüm"';
                Image = ViewDetails;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Show Details action.';

                trigger OnAction()
                begin
                    ShowDetails := not ShowDetails;
                end;
            }
            action("Delete Orphan Lines")
            {
                Caption = 'Delete Orphan Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Delete Selected action.';
                PromotedOnly = true;
                Visible = false;
                trigger OnAction()
                var
                    PackageNoInfoLine: Record "Package No. Info. Line IPK";
                    ConfirmManagement: Codeunit "Confirm Management";
                begin
                    PackageNoInfoLine.SetAutoCalcFields("Total Qty. on Location");
                    PackageNoInfoLine.SetRange("Total Qty. on Location", 0);
                    if PackageNoInfoLine.FindSet() then
                        if ConfirmManagement.GetResponse('Orphan lines will be deleted are you sure ?') then
                            PackageNoInfoLine.DeleteAll(true);


                    // IpekBasicFunctions.DeletePackageLine(PackageNoInfoLine);
                end;
            }
            action("Delete Selected Lines IPK")
            {
                Caption = 'Delete Selected';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Delete Selected action.';
                PromotedOnly = true;
                Visible = false;
                trigger OnAction()
                var
                    PackageNoInfoLine: Record "Package No. Info. Line IPK";
                begin

                    CurrPage.SetSelectionFilter(PackageNoInfoLine);
                    PackageNoInfoLine.FindSet();
                    IpekBasicFunctions.DeletePackageLine(PackageNoInfoLine);
                end;
            }
            action("Adjust Selected Lines IPK")
            {
                Caption = 'Adjust Selected';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Delete Selected action.';
                PromotedOnly = true;
                // Visible = false;
                trigger OnAction()
                var
                    PackageNoInfoLine: Record "Package No. Info. Line IPK";
                // i: Integer;
                begin
                    CurrPage.SetSelectionFilter(PackageNoInfoLine);
                    PackageNoInfoLine.SetAutoCalcFields("Location Code", "Total Qty. on Location");
                    PackageNoInfoLine.SetCurrentKey("Lot No.", "Last Adjustment", "Location Code");
                    PackageNoInfoLine.FindSet();
                    // PackageNoInfoLine.MarkedOnly();
                    // i := PackageNoInfoLine.Count();
                    InventoryAdjustment.TransferPackNoToInventoryAdjustment(PackageNoInfoLine);
                end;
            }


        }
    }
    // trigger OnOpenPage()
    // begin

    // end;

    var
        InventoryAdjustment: Codeunit "Inventory Adjustment IPK";
        IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
        ShowDetails: Boolean;
}