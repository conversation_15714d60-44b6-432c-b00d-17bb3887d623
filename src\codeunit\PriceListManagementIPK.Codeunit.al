codeunit 60011 "Price List Management IPK"
{
    SingleInstance = true;
    procedure CreateNewPriceListLine()
    var
        PriceListLine: Record "Price List Line";
    begin

        LineNo := FindPriceListLineNo(PriceListLine);

        Clear(PriceListLine);
        PriceListLine.Init();
        PriceListLine."Price List Code" := 'S00001';
        PriceListLine."Line No." := LineNo;
        PriceListLine.Insert(true);
        PriceListLine.Validate("Source Type", PriceListLine."Source Type"::Customer);
        PriceListLine.Validate("Source No.", GlobalPriceSource."Source No.");
        PriceListLine.Validate("Asset Type", PriceListLine."Asset Type"::Item);
        PriceListLine.Validate("Amount Type", PriceListLine."Amount Type"::Price);
        PriceListLine.Modify(true);

        Page.Run(Page::"Price List Line Card IPK", PriceListLine);
    end;

    procedure GetSourceNameFromNo(SourceNo: Code[20]): Text[100]
    var
        Customer: Record Customer;
    begin
        Customer.Get(SourceNo);
        exit(Customer.Name);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Price List Line", OnAfterVerify, '', false, false)]
    local procedure "Price List Line_OnAfterVerify"(var PriceListLine: Record "Price List Line")
    begin
        PriceListLine.CalcFields("Units Per Parcel IPK");
        if PriceListLine."Units Per Parcel IPK" = 0 then
            PriceListLine."Piece Unit Price IPK" := PriceListLine."Unit Price" // 1
        else
            PriceListLine."Piece Unit Price IPK" := PriceListLine."Unit Price" / PriceListLine."Units Per Parcel IPK";
    end;

    local procedure FindPriceListLineNo(var PriceListLine: Record "Price List Line"): Integer
    begin
        PriceListLine.SetRange("Price List Code", 'S00001');

        if PriceListLine.FindLast() then
            exit(PriceListLine."Line No." + 10000)
        else
            exit(10000);

    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Price UX Management", OnBeforeShowPriceListLines, '', false, false)]
    local procedure "Price UX Management_OnBeforeShowPriceListLines"(PriceSource: Record "Price Source" temporary; var PriceAsset: Record "Price Asset" temporary; PriceType: Enum "Price Type"; AmountType: Enum "Price Amount Type"; var IsHandled: Boolean)
    begin
        GlobalPriceSource := PriceSource;
    end;

    var
        GlobalPriceSource: Record "Price Source";
        LineNo: Integer;
}