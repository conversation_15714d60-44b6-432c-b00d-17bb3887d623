pageextension 60017 "Item Journal IPK" extends "Item Journal"
{
    layout
    {
        addafter("Lot No.")
        {
            field("Palette Barcode IPK"; Rec."Palette Barcode IPK")
            {
                ApplicationArea = All;
            }
        }
        modify("Variant Code")
        {
            Visible = true;
        }
        modify("Serial No.")
        {
            Visible = false;
        }
        modify("Expiration Date")
        {
            Visible = false;
        }
        modify("Warranty Date")
        {
            Visible = false;
        }
        modify("Bin Code")
        {
            Visible = false;
        }
        addafter("Palette Barcode IPK")
        {
            field("Palette Count IPK"; Rec."Palette Count IPK")
            {
                ApplicationArea = All;
            }
            field("Lot No. IPK"; Rec."Lot No.")
            {
                ApplicationArea = All;
                Caption = 'Lot No. IPK';
                ToolTip = 'Specifies the value of the Lot No. IPK field.';
            }
            field("Inventory Posting Group IPK"; Rec."Inventory Posting Group")
            {
                ApplicationArea = All;
                Editable = true;
                ToolTip = 'Specifies the value of the Inventory Posting Group field.';
            }
        }

    }
    actions
    {
        addafter(Post)
        {
            action("AssignLotAndPaletteNo IPK")
            {
                ApplicationArea = All;
                Caption = 'Assign Lot and Palette No.', Comment = 'TRK="Lot ve Palet No. Ata"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ItemTracking;
                ToolTip = 'Executes the Assign Lot and Palette No. action.';

                trigger OnAction()
                var
                    InventorySetup: Record "Inventory Setup";
                    Item: Record Item;
                    ItemJournalLine: Record "Item Journal Line";
                    NoSeries: Codeunit "No. Series";
                begin
                    InventorySetup.GetRecordOnce();
                    InventorySetup.TestField("Package Nos.");

                    CurrPage.SetSelectionFilter(ItemJournalLine);
                    ItemJournalLine.FindSet(true);
                    repeat
                        Item.Get(ItemJournalLine."Item No.");
                        Item.TestField("Lot Nos.");

                        if ItemJournalLine."Lot No." = '' then
                            ItemJournalLine.Validate("Lot No.", NoSeries.GetNextNo(Item."Lot Nos."));

                        if ItemJournalLine."Palette Barcode IPK" = '' then
                            ItemJournalLine.Validate("Palette Barcode IPK", NoSeries.GetNextNo(InventorySetup."Package Nos."));

                        ItemJournalLine.Modify(true);
                    until ItemJournalLine.Next() = 0;
                end;
            }
        }
    }
}