table 60025 "Inventory Adjustment Line IPK"
{
    Caption = 'Inventory Adjustment Line';
    DataClassification = CustomerContent;
    DrillDownPageId = "Inventory Adjustment Lines IPK";
    LookupPageId = "Inventory Adjustment Lines IPK";
    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Document No.';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(3; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            CaptionClass = '6,1';
            Editable = false;
            ExtendedDatatype = Barcode;
            NotBlank = true;
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(4; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(5; "Line Item No."; Code[20])//hata verebilir kontrol et
        {
            Caption = 'Line Item No.';
            ToolTip = 'Specifies the value of the Line Item No. field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Info. Line IPK"."Line Item No." where("Package No." = field("Package No.")));
        }

        field(6; "Line Variant Code"; Code[10])
        {
            Caption = 'Line Variant Code';
            ToolTip = 'Specifies the value of the Line Variant Code field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Info. Line IPK"."Line Variant Code" where("Package No." = field("Package No.")));
        }
        field(7; "Total Quantity"; Decimal)
        {
            Caption = 'Total Quantity';
            ToolTip = 'Specifies the value of the Total Quantity field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Package No. Info. Line IPK".Quantity where("Package No." = field("Package No.")));
        }
        field(8; "Physical Quantity"; Decimal)
        {
            Caption = 'Physical Quantity';
            MinValue = 0;
            ToolTip = 'Specifies the value of the Total Quantity field.';
            trigger OnValidate()
            begin

                Rec.CalcFields("Total Quantity");
                Rec.Validate("Quantity Difference", Rec."Physical Quantity" - Rec."Total Quantity");

                Rec.Modify(true);
            end;

        }
        field(9; "Quantity Difference"; Decimal)
        {
            Caption = 'Quantity Difference';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Quantity Difference field.';
            trigger OnValidate()
            begin
                case true of
                    Rec."Quantity Difference" > 0:
                        Rec.Validate("Adjustment Type", Rec."Adjustment Type"::"Positive Adjustment"); // Positive difference
                    Rec."Quantity Difference" < 0:
                        Rec.Validate("Adjustment Type", Rec."Adjustment Type"::"Negative Adjustment"); // Negative difference
                    else
                        Rec.Validate("Adjustment Type", Rec."Adjustment Type"::"No Change"); // Zero difference
                end;
            end;
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the No. Series field.';
        }
        field(10; "Current Location Code"; Code[10])
        {
            Caption = 'Current Location Code';
            AllowInCustomizations = Always;
            TableRelation = Location.Code;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Location Code IPK" where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(11; "Adjustment Type"; Enum "Inventory Adjustment Type IPK")
        {
            Caption = 'Adjustment type';
            Editable = false;
            // InitValue = "No Change";
            ToolTip = 'Specifies the value of the Adjustment type field.';
        }
        field(12; "Line Item Uom. Code"; Code[10])//hata verebilir kontrol et
        {
            Caption = 'Line Item Unit Of Measure Code';
            ToolTip = 'Specifies the value of the Line Item Unit Of Measure Code field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Info. Line IPK"."Unit of Measure Code" where("Package No." = field("Package No.")));
        }
        field(13; "Line Item Description"; Text[100])
        {
            Caption = 'Line Item Description';
            ToolTip = 'Specifies the value of theLine Item Description field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item.Description where("No." = field("Line Item No.")));
        }
        field(14; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            AllowInCustomizations = Always;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Info. Line IPK"."Lot No." where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(key2; "Package No.", "Physical Quantity")
        {
        }
    }
    trigger OnInsert()
    var
        InvAdjLine: Record "Inventory Adjustment Line IPK";
    begin
        InvAdjLine.SetRange("Document No.", Rec."Document No.");
        if InvAdjLine.FindLast() then
            Rec."Line No." := InvAdjLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}