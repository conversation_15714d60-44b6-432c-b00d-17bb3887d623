page 60013 "Prod.Order Line - Lot No. IPK"
{
    ApplicationArea = All;
    Caption = 'Prod. Order Line - Lot No.';
    PageType = List;
    SourceTable = "Prod.Order Line - Lot No. IPK";
    UsageCategory = Lists;
    DelayedInsert = true;
    // Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Prod Order No."; Rec."Prod. Order No.")
                {
                    ToolTip = 'Specifies the value of the Production Order No. field.';
                }
                field("Prod. Order Line No."; Rec."Prod. Order Line No.")
                {
                }
                field("Start Date-Time"; Rec."Start Date-Time")
                {
                }
                field("End Date-Time"; Rec."End Date-Time")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
            }
        }
    }
}