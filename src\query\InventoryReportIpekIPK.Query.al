query 60000 "Inventory Report Ipek IPK"
{
    Caption = 'Inventory Report Ipek';
    QueryType = Normal;

    elements
    {
        dataitem(Item; Item)
        {
            column(No; "No.")
            {
            }
            column(Description; Description)
            {
            }
            column(ItemCategoryCode; "Item Category Code")
            {
            }
            column(ItemCategoryDescriptionIPK; "Item Category Description IPK")
            {
            }
            column(UnitsperParcel; "Units per Parcel")
            {
            }
            column(ExportDomesticIPK; "Export/Domestic IPK")
            {
            }
            dataitem(Item_Ledger_Entry; "Item Ledger Entry")
            {
                DataItemLink = "Item No." = Item."No.";


                column(LocationCode; "Location Code")
                {
                }
                column(Quantity; Quantity)
                {
                }
                column(RemainingQuantity; "Remaining Quantity")
                {
                }
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}