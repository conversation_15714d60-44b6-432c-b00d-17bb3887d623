page 60024 "Package Transfer Subpage MXW"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Subpage';
    PageType = ListPart;
    SourceTable = "Package Transfer Line MXW";
    InsertAllowed = false;
    SourceTableView = sorting("Document No.", "Line No.") order(descending);

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                    QuickEntry = false;

                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.SetRange("Package No.", Rec."Package No.");
                        if PackageNoInformation.FindFirst() then
                            Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                    end;
                }

                field("Item No."; Rec."Item No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                    QuickEntry = false;
                }

                field("Variant Code"; Rec."Variant Code")
                {
                    ApplicationArea = All;
                    Editable = false;
                    QuickEntry = false;
                }

                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    Editable = false;
                    QuickEntry = false;
                }

                field("Current Quantity"; Rec.Quantity)
                {
                    ApplicationArea = All;
                    Caption = 'Current Quantity';
                    ToolTip = 'Specifies the value of the Current Quantity field.';
                    Editable = false;
                    QuickEntry = false;
                }

                field("Quantity To Transfer"; Rec."Quantity To Transfer")
                {
                    ApplicationArea = All;
                    QuickEntry = false;
                }

                field("Lot No."; Rec."Lot No.")
                {
                    ApplicationArea = All;
                    Editable = false;
                    QuickEntry = false;
                }

                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(AssignMaxQuantity)
            {
                Caption = 'Assign Max Quantity';
                Image = ServiceAccessories;
                ToolTip = 'Assigns the maximum available quantity to transfer.';

                trigger OnAction()
                begin
                    Rec.Validate("Quantity To Transfer", Rec.Quantity);
                    Rec.Modify(true);
                end;
            }
        }
    }

    // trigger OnAfterGetRecord()
    // var
    //     PackageNoInformation: Record "Package No. Information";
    // begin
    //     PackageNoInformation.SetAutoCalcFields(Inventory);
    //     if PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.") then
    //         LiveQuantity := PackageNoInformation.Inventory
    //     else
    //         LiveQuantity := 0;
    // end;

    // var
    //     LiveQuantity: Decimal;
}
