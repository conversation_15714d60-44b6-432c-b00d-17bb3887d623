report 60006 "Palette Label IPK"
{
    ApplicationArea = All;
    Caption = 'Palette Label';
    UsageCategory = ReportsAndAnalysis;
    DefaultLayout = RDLC;
    RDLCLayout = 'src/reportlayout/PaletteLabelIPK.Report.rdlc';
    dataset
    {
        dataitem(PackageNoInformation; "Package No. Information")
        {
            column(PackageNo; "Package No.")
            {
            }
            column(LocationCodeIPK; "Location Code IPK")
            {
            }
            column(PackageNoBarCode; PackageNoBarCode)
            {
            }
            column(PackageNoQRCode; PackageNoQRCode)
            {
            }
            column(CreatedAtIPK_PackageNoInformation; "Created At IPK")
            {
            }
            column(DocumentNoIPK_PackageNoInformation; "Document No. IPK")
            {
            }
            column(ProducedByIPK_PackageNoInformation; "Produced By IPK")
            {
            }

            dataitem("Package No. Info. Line IPK"; "Package No. Info. Line IPK")
            {
                DataItemLink = "Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Package No." = field("Package No.");

                column(Description_PackageNoInfoLineIPK; Description)
                {
                }
                column(LineItemNo_PackageNoInfoLineIPK; "Line Item No.")
                {
                }
                column(LineVariantCode_PackageNoInfoLineIPK; "Line Variant Code")
                {
                }
                column(LotNo_PackageNoInfoLineIPK; "Lot No.")
                {
                }
                column(Quantity_PackageNoInfoLineIPK; Quantity)
                {
                }
            }
            trigger OnAfterGetRecord()
            var
                BarcodeFontProvider: Interface "Barcode Font Provider";
                BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
                BarcodeString: Text;

            begin

                BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;

                // Item.SetLoadFields(Item.Description);
                // Item.Get("Item No.");
                // Description := Item.Description;


                if "Package No." <> '' then begin
                    BarcodeString := "Package No.";

                    BarcodeFontProvider.ValidateInput(BarcodeString, BarcodeSymbology);

                    PackageNoBarCode := BarcodeFontProvider.EncodeFont(BarcodeString, BarcodeSymbology);
                    PackageNoQRCode := BarcodeFontProvider2D.EncodeFont(BarcodeString, BarcodeSymbology2D);
                end
            end;
        }
    }
    trigger OnInitReport()
    begin
        BarcodeSymbology := Enum::"Barcode Symbology"::Code128;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
    end;

    var
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        PackageNoBarCode: Text;
        PackageNoQRCode: Text;
}