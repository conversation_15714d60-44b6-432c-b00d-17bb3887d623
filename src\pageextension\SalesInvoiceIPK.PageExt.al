pageextension 60011 "Sales Invoice IPK" extends "Sales Invoice"
{

    layout
    {
        addafter("External Document No.")
        {
            field("Export No. IPK"; Rec."Export No. IPK")
            {
                ApplicationArea = All;
            }

        }
        addafter("Use Header Q/O Curr. Code INF")
        {
            field("Freight Amount IPK"; Rec."Freight Amount IPK")
            {
                ApplicationArea = All;
            }
        }

    }
    actions
    {
        addfirst("F&unctions")
        {
            action("CalcualteCFRUnitPrices IPK")
            {
                ApplicationArea = All;
                Caption = 'Calculate FOB Unit Prices';
                Image = CalculateCost;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Calculate FOB Unit Prices action.';

                trigger OnAction()
                begin
                    IpekSalesManagement.CalculateFOBUnitPriceFromSalesHeader(Rec);
                end;
            }
            action("CreateFreightItemChargeLine IPK")
            {
                ApplicationArea = All;
                Caption = 'Create Freight Charge(Item) Line';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = SuggestFinancialCharge;
                ToolTip = 'Create Freight Charge(Item) Line.';
                // Visible = true;
                trigger OnAction()
                begin
                    IpekSalesManagement.InsertFreightItemChargeLineFromSalesHeader(Rec);
                    CurrPage.Update(true);

                end;
            }

        }

    }
    var
        IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
}