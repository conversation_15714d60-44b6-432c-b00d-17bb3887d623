pageextension 60074 "Production BOM IPK" extends "Production BOM"
{
    layout
    {
        addafter(Status)
        {

            field("Approval Status IPK"; Rec."Approval Status IPK")
            {
                ApplicationArea = All;
                Editable = false;
                ValuesAllowed = 5, 1, 4, 3;
            }
            field("Category Code IPK"; Rec."Category Code IPK")
            {
                ApplicationArea = All;
            }
            field("Category Description IPK"; Rec."Category Description IPK")
            {
                ApplicationArea = All;
            }
        }
        modify(Status)
        {
            Editable = false;
        }
    }
    actions
    {
        addafter("&Prod. BOM")
        {
            action("Request Approval IPK")
            {
                Caption = 'Request Approval';
                Image = SendApprovalRequest;
                ApplicationArea = All;
                ToolTip = 'Executes the Request Approval action.';
                trigger OnAction()
                begin
                    Rec.Status := Rec.Status::"Under Development";
                    Rec.Validate("Approval Status IPK", Rec."Approval Status IPK"::Open);
                end;
            }
            action("ApproveBOM IPK")
            {
                Caption = 'Approve BOM';
                Image = Approval;
                Visible = false;
                ApplicationArea = All;
                ToolTip = 'Executes the Approve BOM action.';
                trigger OnAction()
                begin
                    Rec.Status := Rec.Status::Certified;
                    Rec."Approval Status IPK" := Rec."Approval Status IPK"::Approved;
                end;

            }
            action("RejectBOM IPK")
            {
                Caption = 'Reject BOM';
                Image = Reject;
                Visible = false;
                ApplicationArea = All;
                ToolTip = 'Executes the Reject BOM action.';
                trigger OnAction()
                begin
                    Rec.Status := Rec.Status::"Under Development";
                    Rec."Approval Status IPK" := Rec."Approval Status IPK"::Rejected;
                end;

            }
        }

    }
}