report 60000 "Proforma Invoice IPK"
{
    ApplicationArea = All;
    Caption = 'Proforma Invoice';
    UsageCategory = ReportsAndAnalysis;
    DefaultLayout = RDLC;
    RDLCLayout = 'src/reportLayout/TempRDLC.RDLC';
    dataset
    {
        dataitem(SalesHeader; "Sales Header")
        {
            column(SelltoCustomerNo; "Sell-to Customer No.")
            {
            }
            column(SelltoCustomername; "Sell-to Customer Name")
            {
            }
            column(BilltoAddress; "Bill-to Address")
            {
            }
            column(BilltoAddress2; "Bill-to Address 2")
            {
            }
            column(BilltoCity; "Bill-to City")
            {
            }
            dataitem("Country/Region"; "Country/Region")
            {
                DataItemLink = Code = field("Bill-to Country/Region Code");
                column(NameCountryRegion; Name)
                {
                }
            }
            column(YourReference; "Your Reference")
            {
            }
            column(TransportMethod; "Transport Method")
            {
            }
            column(ShipmentDate; "Shipment Date")
            {
            }
            column(PaymentTermsCode; "Payment Terms Code")
            {
            }
            dataitem("Payment Term Translation"; "Payment Term Translation")
            {
                DataItemLink = "Payment Term" = field("Payment Terms Code"), "Language Code" = field("Language Code");
                column(PaymentTermsDescription; Description)
                {
                }
            }
            column(NoSalesHeader; "No.")
            {
            }
            column(DocumentDateSalesHeader; "Document Date")
            {
            }
            column(NoLbl; NoLbl)
            {
            }
            column(YourReferenceLbl; YourReferenceLbl)
            {
            }
            column(TransportMethodLbl; TransportMethodLbl)
            {
            }
            column(ShipmentDateLbl; ShipmentDateLbl)
            {
            }
            column(PaymentTermsCodeLbl; PaymentTermsCodeLbl)
            {
            }

            column(CurrencyCodeSalesHeader; "Currency Code")
            {
            }
            dataitem(CompanyBankAccountCode; "Bank Account")
            {
                DataItemLink = "No." = field("Company Bank Account Code");
                column(Name; Name)
                {
                }
                column(SWIFTCode; "SWIFT Code")
                {
                }
                column(BankAccountNo; "Bank Account No.")
                {
                }
                column(BankBranchNo_CompanyBankAccountCode; "Bank Branch No.")
                {
                }
                column(IBAN; IBAN)
                {
                }

                column(NameLbl; NameLbl)
                {
                }
                column(SWIFTCodeLbl; SWIFTCodeLbl)
                {
                }
                column(BankAccountNoLbl; BankAccountNoLbl)
                {
                }
                column(IBANLbl; IBANLbl)
                {
                }

            }
            dataitem("Sales Line"; "Sales Line")
            {
                DataItemLink = "Document Type" = field("Document Type"), "Document No." = field("No.");
                DataItemTableView = where(Type = const(Item));
                column(ItemNoSalesLine; "No.")
                {
                }
                column(DescriptionSalesLine; Description)
                {
                }
                column(LineUnitQuantityIPKSalesLine; "Line Unit Quantity IPK")
                {
                }
                column(QuantitySalesLine; Quantity)
                {
                }
                column(UnitPriceSalesLine; "Unit Price")
                {
                }
                column(AmountSalesLine; Amount)
                {
                }
                column(DescriptionSalesLineLbl; DescriptionSalesLineLbl)
                {
                }
                column(LineUnitQuantityIPKSalesLineLbl; LineUnitQuantityIPKSalesLineLbl)
                {
                }
                column(QuantitySalesLineLbl; QuantitySalesLineLbl)
                {
                }
                column(UnitPriceSalesLineLbl; UnitPriceSalesLineLbl)
                {
                }
                column(AmountSalesLineLbl; AmountSalesLineLbl)
                {
                }
                column(ItemNoSalesLineLbl; ItemNoSalesLineLbl)
                {
                }
                column(UnitofMeasureCodeSalesLine; "Unit of Measure Code")
                {
                }

            }
        }

    }
    requestpage
    {
        layout
        {
            area(Content)
            {

            }
        }
        actions
        {
            area(Processing)
            {
            }
        }
    }
    var
        AmountSalesLineLbl: Label 'Total';
        BankAccountNoLbl: Label 'Account No';
        DescriptionSalesLineLbl: Label 'Description';
        IBANLbl: Label 'IBAN';
        ItemNoSalesLineLbl: Label 'Code';
        LineUnitQuantityIPKSalesLineLbl: Label 'Carton';
        NameLbl: Label 'Bank';
        NoLbl: Label 'Document No';
        PaymentTermsCodeLbl: Label 'Payment';
        QuantitySalesLineLbl: Label 'Quantity';
        ShipmentDateLbl: Label 'Delivery Date';
        SWIFTCodeLbl: Label 'Swift Code';
        TransportMethodLbl: Label 'Delivery Term';
        UnitPriceSalesLineLbl: Label 'Unit Price';
        YourReferenceLbl: Label 'Customer Order No';
}