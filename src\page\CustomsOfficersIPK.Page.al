page 60010 "Customs Officers IPK"
{
    ApplicationArea = All;
    Caption = 'Customs Officers';
    PageType = List;
    SourceTable = "Customs Officer IPK";
    UsageCategory = ReportsAndAnalysis;

    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field("Code"; Rec."Code")
                {
                }
                field("Contact Person"; Rec."Contact Person")
                {
                }
                field(Name; Rec.Name)
                {
                }
                field(Phone; Rec.Phone)
                {
                }
            }
        }
    }
}