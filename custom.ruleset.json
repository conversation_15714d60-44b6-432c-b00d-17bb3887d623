{"name": "Maxwell Custom Ruleset", "description": "Custom ruleset for Maxwell Customizations project", "includedRuleSets": [{"action": "<PERSON><PERSON><PERSON>", "path": "https://raw.githubusercontent.com/StefanMaron/RulesetFiles/main/pte.rulset.json"}], "rules": [{"id": "AA0073", "action": "Warning", "justification": "Obsolete tag missing: Use InfoClass.Warning instead of Warning for Info level messages"}, {"id": "AA0215", "action": "Warning", "justification": "Follow the style guide about line endings"}, {"id": "LC0068", "action": "None", "justification": "Disabled as requested."}, {"id": "LC0084", "action": "None", "justification": "Disabled as requested."}, {"id": "LC0023", "action": "None", "justification": "Disabled as requested."}]}