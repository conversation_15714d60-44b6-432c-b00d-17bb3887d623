page 60001 "Load Card IPK"
{
    ApplicationArea = All;
    Caption = 'Load Card';
    PageType = Document;
    SourceTable = "Load Header IPK";
    Extensible = true;
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Document No."; Rec."Warehouse Shipment No.")
                {
                    ToolTip = 'Specifies the value of the Warehouse Shipment No. field.';
                }
                field("No."; Rec."No.")
                {
                    Editable = false;
                }
                field(Status; Rec.Status)
                {
                }
                field("Load Method"; Rec."Load Method")
                {
                }
                field("Load Method Type"; Rec."Load Method Type")
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Container No."; Rec."Container No.")
                {
                }
                field("Seal No."; Rec."Seal No.")
                {
                }
                field("Driver No."; Rec."Driver No.")
                {
                }
                field("Driver Name"; Rec."Driver Name")
                {
                }
                field("Driver Surname"; Rec."Driver Surname")
                {
                }
                field("Driver VAT Registration No."; Rec."Driver VAT Registration No.")
                {
                }
                field("Domestic Shipping Agent Code"; Rec."Domestic Shipping Agent Code")
                {
                }
                field("Truck License Plate"; Rec."Truck License Plate")
                {
                }
                field("Trailer License Plate"; Rec."Trailer License Plate")
                {
                }
                field("Posted Document No."; Rec."Posted Document No.")
                {
                }
                field(Mix; Rec.Mix)
                {
                }
                field("Starting Date-Time"; Rec."Starting Date-Time")
                {
                    ToolTip = 'Specifies the value of the Starting Date-Time field.';
                }
                field("Ending Date-Time"; Rec."Ending Date-Time")
                {
                }
                field("Partial Loading"; Rec."Partial Loading")
                {
                }
                field(Notes; Rec.Notes)
                {
                    MultiLine = true;
                }
                field("E-Shipment No."; Rec."E-Shipment No.")
                {
                }
                // field("Posted Warehouse Shipment No."; Rec."Posted Warehouse Shipment No.")
                // {
                // }
                field("Total Net Weight"; Rec."Total Net Weight")
                {
                }
                field("Total Gross Weight"; Rec."Total Gross Weight")
                {
                }
                field("Total Volume"; Rec."Total Volume")
                {
                }
                field("Total Unit Quantity"; Rec."Total Unit Quantity")
                {
                }

                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Load Location"; Rec."Load Location")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Ship-to Code"; Rec."Ship-to Code")
                {
                }
                field("Ship-to Name"; Rec."Ship-to Name")
                {
                }

            }
            usercontrol(SetFieldFocus; "SetFieldFocus IPK")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Label Text');
                end;
            }
            group(Barcode)
            {
                Caption = 'Read Barcode';
                field("Label Text"; Rec."Label Text")
                {
                    trigger OnValidate()
                    begin
                        // LastReadBarcode := Rec."Label Text";

                        CurrPage.Update();

                        CurrPage.SetFieldFocus.SetFocusOnField('Label Text');
                    end;
                }
                field("Last Read Barcode"; Rec."Last Read Barcode")
                {
                    Caption = 'Last Barcode';
                    ToolTip = 'Specifies the last barcode read.';
                    Editable = false;
                }
                field("Last Read Quantity"; Rec."Last Read Quantity")
                {
                    Caption = 'Last Quantity';
                    ToolTip = 'Specifies the last Quantity.';
                    Editable = false;
                }
            }
            part(PlanningLines; "Load Planning Subpage IPK")
            {
                Caption = 'Planning Lines';
                SubPageLink = "Warehouse Shipment No." = field("Warehouse Shipment No."), "Document No." = field("No.");
                Editable = Rec.Status = Rec.Status::" ";
                UpdatePropagation = Both;
            }
            part(Lines; "Load Line Subpage IPK")
            {
                Caption = 'Lines';
                Visible = false;
                SubPageLink = "Warehouse Shipment No." = field("Warehouse Shipment No."), "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Ship)
            {
                Caption = 'Ship';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Post;
                Enabled = (Rec.Status = Rec.Status::Loaded);
                ToolTip = 'Ship the Load Card.';
                PromotedOnly = true;
                trigger OnAction()
                begin
                    IpekSalesManagement.ShipLoadHeader(Rec);
                end;
            }
            action(ViewReadBarcodes)
            {
                Caption = 'View Read Barcodes', Comment = 'TRK="Okutulmuş Barkodlar"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                RunObject = page "Load Lines IPK";
                RunPageLink = "Warehouse Shipment No." = field("Warehouse Shipment No."), "Document No." = field("No.");
                ToolTip = 'View the read barcodes.';
            }
        }
        // area(Reporting)
        // {
        //     action(PrintBoL)
        //     {
        //         ApplicationArea = All;
        //         Caption = 'Print Bill of Lading';
        //         Promoted = true;
        //         PromotedCategory = Report;
        //         PromotedIsBig = true;
        //         Image = Print;
        //         ToolTip = 'Print Bill of Lading.';
        //         PromotedOnly = true;
        //         trigger OnAction()
        //         begin
        //             Rec.SetRecFilter();
        //             Report.Run(Report::"Bill Of Lading IPK", true, true, Rec);
        //         end;
        //     }
        // }
    }
    var
        IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
    //LastReadBarcode: Code[50];
}