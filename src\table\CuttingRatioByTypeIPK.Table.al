table 60031 "Cutting Ratio By Type IPK"
{
    Caption = 'Cutting Ratio By Type';
    DataClassification = ToBeClassified;
    DrillDownPageId = "Cutting Ratio By Type IPK";
    LookupPageId = "Cutting Ratio By Type IPK";

    fields
    {
        field(1; "Cutting Category Type"; Enum "Cutting Category Type IPK")
        {
            Caption = 'Cutting Category Type';
            ToolTip = 'Specifies the value of the "Cutting Category Type field.';
        }
        field(2; "Cutting Ratio"; Decimal)
        {
            Caption = 'Cutting Ratio';
            ToolTip = 'Specifies the value of the Cutting Ratio field.';
            MinValue = 0;
            MaxValue = 1;
        }
    }
    keys
    {
        key(PK; "Cutting Category Type")
        {
            Clustered = true;
        }
    }
}