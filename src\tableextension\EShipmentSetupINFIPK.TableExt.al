tableextension 60025 "E-Shipment Setup INF IPK" extends "E-Shipment Setup INF"
{
    fields
    {
        field(60000; "Delivery Cust. Name IPK"; Text[151])
        {
            Caption = 'Delivery Cust. Name';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies Delivery Customer Name.';
        }
        field(60001; "Delivery Cust. Street Name IPK"; Text[151])
        {
            Caption = 'Delivery Cust. Street Name';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies Delivery Customer Street Name.';
        }
        field(60002; "Delivery Cust. Country IPK"; Text[50])
        {
            Caption = 'Delivery Cust. Country';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies Delivery Customer Country.';
        }
        field(60003; "Delivery Cust. City Name IPK"; Text[30])
        {
            Caption = 'Delivery Cust. City Name';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies Delivery Customer City Name.';
        }
        field(60004; "Delivery Cust. Postal Zone IPK"; Text[20])
        {
            Caption = 'Delivery Cust. Postal Zone';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies Delivery Customer Postal Zone.';
        }
        field(60005; "Del. Cust. Tax Schema Name IPK"; Text[100])
        {
            Caption = 'Delivery Cust. Tax Schema Name';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies Delivery Customer Tax Schema Name.';
        }
        field(60006; "Del. Cust. City Subdiv. IPK"; Text[30])
        {
            Caption = 'Delivery Cust. City Subdiv.';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies Delivery Customer City Subdivision.';
        }
        field(60007; "Del. Cust. Country Code IPK"; Code[10])
        {
            Caption = 'Delivery Cust. Country Code';
            DataClassification = CustomerContent;
            TableRelation = "Country/Region".Code;
            ToolTip = 'Specifies Delivery Cust. Country Code.';
        }
    }
}

