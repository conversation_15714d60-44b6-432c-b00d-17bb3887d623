page 60041 "Package Combine Subpage IPK"
{
    ApplicationArea = All;
    Caption = 'Package Combine Subpage';
    PageType = ListPart;
    SourceTable = "Package Combine Line IPK";
    UsageCategory = Lists;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field(Description; Rec.Description)
                {
                    Editable = false;
                }
                // field("Item Category Description"; Rec."Item Category Description")
                // {
                //     Editable = false;
                // }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                }
                field("Qty. To Add"; Rec."Quantity-to Add")
                {
                    Editable = true;
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
            }
        }
    }
}