pageextension 60078 "Price List Line Review IPK" extends "Price List Line Review"
{
    // CardPageId = "Price List Line Card IPK";
    layout
    {
        addafter("Unit Price")
        {

            field("Line Unit Price IPK"; Rec."Piece Unit Price IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Line Unit Price field.';
            }
        }
        addafter("Cost Factor")
        {

            field("Units Per Parcel IPK"; Rec."Units Per Parcel IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Line Unit Price field.';
            }
        }
    }

    actions
    {
        addbefore(OpenPriceList)
        {
            action("Create New IPK")
            {
                ApplicationArea = All;
                Caption = 'Create New', Comment = 'TRK="Yeni Oluştur"';
                ToolTip = 'Creates a new price list line for the selected item.';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = NewRow;

                trigger OnAction()

                begin
                    // CurrPage.Update();
                    PriceListManagement.CreateNewPriceListLine();

                    CurrPage.Close();
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        // Rec.Validate("Unit Price");
        // Rec.CalcFields("Units Per Parcel IPK");
        // if Rec."Units Per Parcel IPK" = 0 then
        //     Rec."Piece Unit Price IPK" := Rec."Unit Price" // 1
        // else
        //     Rec."Piece Unit Price IPK" := Rec."Unit Price" / Rec."Units Per Parcel IPK";
    end;

    var
        PriceListManagement: Codeunit "Price List Management IPK";
}