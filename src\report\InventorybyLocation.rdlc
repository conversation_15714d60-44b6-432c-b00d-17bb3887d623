﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="DocumentNo">
          <DataField>DocumentNo</DataField>
        </Field>
        <Field Name="LineNo">
          <DataField>LineNo</DataField>
        </Field>
        <Field Name="PackageNo">
          <DataField>PackageNo</DataField>
        </Field>
        <Field Name="LocationCode">
          <DataField>LocationCode</DataField>
        </Field>
        <Field Name="LineItemNo">
          <DataField>LineItemNo</DataField>
        </Field>
        <Field Name="LineVariantCode">
          <DataField>LineVariantCode</DataField>
        </Field>
        <Field Name="TotalQuantity">
          <DataField>TotalQuantity</DataField>
        </Field>
        <Field Name="TotalQuantityFormat">
          <DataField>TotalQuantityFormat</DataField>
        </Field>
        <Field Name="PhysicalQuantity">
          <DataField>PhysicalQuantity</DataField>
        </Field>
        <Field Name="PhysicalQuantityFormat">
          <DataField>PhysicalQuantityFormat</DataField>
        </Field>
        <Field Name="QuantityDifference">
          <DataField>QuantityDifference</DataField>
        </Field>
        <Field Name="QuantityDifferenceFormat">
          <DataField>QuantityDifferenceFormat</DataField>
        </Field>
        <Field Name="CurrentLocationCode">
          <DataField>CurrentLocationCode</DataField>
        </Field>
        <Field Name="AdjustmentType">
          <DataField>AdjustmentType</DataField>
        </Field>
        <Field Name="LineItemUomCode">
          <DataField>LineItemUomCode</DataField>
        </Field>
        <Field Name="LineItemDescription">
          <DataField>LineItemDescription</DataField>
        </Field>
        <Field Name="NoSeries">
          <DataField>NoSeries</DataField>
        </Field>
        <Field Name="SystemCreatedAt">
          <DataField>SystemCreatedAt</DataField>
        </Field>
        <Field Name="SystemCreatedBy">
          <DataField>SystemCreatedBy</DataField>
        </Field>
        <Field Name="SystemId">
          <DataField>SystemId</DataField>
        </Field>
        <Field Name="SystemModifiedAt">
          <DataField>SystemModifiedAt</DataField>
        </Field>
        <Field Name="SystemModifiedBy">
          <DataField>SystemModifiedBy</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>