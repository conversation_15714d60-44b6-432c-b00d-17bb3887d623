table 60000 "Maxwell Setup MXW"
{
    Caption = 'Maxwell Setup';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        // field(2; "Package Nos."; Code[20])
        // {
        //     Caption = 'Package Nos.';
        //     TableRelation = "No. Series".Code;
        //     ToolTip = 'Specifies the number series for package numbers.';
        // }
        // field(3; "Default Warehouse Location"; Code[10])
        // {
        //     Caption = 'Default Warehouse Location';
        //     TableRelation = Location.Code;
        //     ToolTip = 'Specifies the default warehouse location.';
        // }
        // field(4; "Quality Control Required"; Boolean)
        // {
        //     Caption = 'Quality Control Required';
        //     ToolTip = 'Specifies whether quality control is required for warehouse receipts.';
        // }
        // field(5; "Auto Assign Item Tracking"; Boolean)
        // {
        //     Caption = 'Auto Assign Item Tracking';
        //     ToolTip = 'Specifies whether to automatically assign item tracking information.';
        // }
        // field(6; "Quality Control Nos."; Code[20])
        // {
        //     Caption = 'Quality Control Nos.';
        //     TableRelation = "No. Series".Code;
        //     ToolTip = 'Specifies the number series for quality control documents.';
        // }
        field(7; "Package Transfer Nos. MXW"; Code[20])
        {
            Caption = 'Package Transfer Nos.';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the number series for package transfer documents.';
        }
        field(8; "Package Tran. Jnl. Temp. MXW"; Code[10])
        {
            Caption = 'Package Transfer Journal Template Name';
            ToolTip = 'Specifies the journal template name for package transfers.';
            TableRelation = "Item Journal Template".Name where(Type = const(Transfer));
        }
        field(9; "Package Tran. Jnl. Batch MXW"; Code[10])
        {
            Caption = 'Package Transfer Journal Batch Name';
            ToolTip = 'Specifies the journal batch name for package transfers.';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Tran. Jnl. Temp. MXW"));
        }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    var
        RecordHasBeenRead: Boolean;

    procedure GetRecordOnce()
    begin
        if RecordHasBeenRead then
            exit;
        Get();
        RecordHasBeenRead := true;
    end;

    procedure InsertIfNotExists()
    begin
        Reset();
        if not Get() then begin
            Init();
            Insert(true);
        end;
    end;
}
