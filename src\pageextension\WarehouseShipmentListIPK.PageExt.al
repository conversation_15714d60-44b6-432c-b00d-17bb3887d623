pageextension 60005 "Warehouse Shipment List IPK" extends "Warehouse Shipment List"
{
    layout
    {
        addafter("No.")
        {
            field("External Document No. IPK"; Rec."External Document No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies a document number that refers to the customer''s or vendor''s numbering system.';
            }
            field("Sell-to Source No. INF IPK"; Rec."Sell-to Source No. INF")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Sell-to Source No. field.';
            }
            field("Source No. INF IPK"; Rec."Source No. INF")
            {
                ApplicationArea = All;
#pragma warning disable LC0066
                ToolTip = 'Specifies the value of the Source No. field.';
#pragma warning restore LC0066
            }
            field("Salesperson Code IPK"; Rec."Salesperson Code IPK")
            {
                ApplicationArea = All;
            }
            field("Ship-to Code INF IPK"; Rec."Ship-to Code INF")
            {
                ApplicationArea = All;
#pragma warning disable LC0066
                ToolTip = 'Specifies the value of the Ship-to Code field.';
#pragma warning restore LC0066
            }
            field("Ship-to Name INF IPK"; Rec."Ship-to Name INF")
            {
                ApplicationArea = All;
#pragma warning disable LC0066
                ToolTip = 'Specifies the value of the Ship-to Name field.';
#pragma warning restore LC0066
            }
            field("Priority Shipment IPK"; Rec."Priority Shipment IPK")
            {
                ApplicationArea = All;
            }

        }
        modify("Posting Date")
        {
            Visible = true;
        }
    }
}