pageextension 60076 "E-Shipment INF IPK" extends "E-Shipment INF"
{
    actions
    {
        addafter(AdditionalDocuments)
        {
            action("OpenLoadCard IPK")
            {
                ApplicationArea = All;
                Caption = 'Open Load Card', Comment = 'TRK="Yük Kartını Aç"';
                Image = Bin;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Open Load Card action.';

                trigger OnAction()
                var
                    LoadHeader: Record "Load Header IPK";
                    LoadLine: Record "Load Line IPK";
                begin
                    LoadHeader.SetRange("Posted Document No.", Rec."No.");
                    LoadHeader.FindFirst();
                    LoadLine.SetRange("Document No.", LoadHeader."No.");
                    LoadLine.FindSet();
                    Page.Run(Page::"Load Lines IPK", LoadLine);
                end;
            }
        }
    }
}