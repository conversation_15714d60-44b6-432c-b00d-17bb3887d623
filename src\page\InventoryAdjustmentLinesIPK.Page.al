page 60062 "Inventory Adjustment Lines IPK"
{
    ApplicationArea = All;
    Caption = 'Inventory Adjustment Lines';
    PageType = List;
    SourceTable = "Inventory Adjustment Line IPK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Line Item No."; Rec."Line Item No.")
                {
                }
                field("Line Description"; Rec."Line Item Description")
                {
                    ToolTip = 'Specifies the value of the Line Item Description field.';
                }
                field("Line Item Uom. Code"; Rec."Line Item Uom. Code")
                {
                    ToolTip = 'Specifies the value of the Line Item No. field.';
                }

                field("Line Variant Code"; Rec."Line Variant Code")
                {
                }
                field("Total Quantity"; Rec."Total Quantity")
                {
                }
                field("Physical Quantity"; Rec."Physical Quantity")
                {
                    ToolTip = 'Specifies the value of the Physical Quantity field.';
                }
                field("Quantity Difference"; Rec."Quantity Difference")
                {
                }
                field("Current Location Code"; Rec."Current Location Code")
                {
                    ToolTip = 'Specifies the value of the Current Location Code field.';
                }
                field("Adjustment Type"; Rec."Adjustment Type")
                {
                }
                field("No. Series"; Rec."No. Series")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
            }
        }
    }
}