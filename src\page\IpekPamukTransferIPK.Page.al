page 60023 "Ipek Pamuk Transfer IPK"
{
    ApplicationArea = All;
    Caption = 'Koltukaltı Ambarı Transfer', Comment = 'Koltukaltı Ambarı Transfer';
    // CaptionML = TRK = 'Paket Transfer', ENU = 'Packet Transfer';
    PageType = CardPart;
    SourceTable = "Ipek Pamuk Activity Cue IPK";
    RefreshOnActivate = true;
    layout
    {
        area(Content)
        {
            group(CreateNewPackageTransferActionGroup1)
            {
                Caption = 'DISK';
                ShowCaption = false;
                cuegroup(CreateNewPackageTransferCueGroup1)
                {
                    Caption = 'DISK';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateNewPackageTransfer1)
                        {
                            Caption = 'DISK', Comment = 'DİSK';

                            // CaptionML = TRK = 'DİSK', ENU = 'DISK';
                            Image = TileGreen;

                            // RunObject = page "Process Quality Control Doc.";
                            ToolTip = 'Executes the Approve Create New Package Transfer action.';
                            trigger OnAction()
                            var
                                Location: Record Location;
                                PackageTransferHeader: Record "Package Transfer Header IPK";
                            begin
                                Location.SetRange("Role Center Button Order IPK", 1);

                                Location.FindFirst();
                                PackageTransferHeader.Init();
                                PackageTransferHeader.Insert(true);
                                PackageTransferHeader.Validate("Transfer-to Code", Location.Code);
                                PackageTransferHeader.Modify(true);
                                Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);
                            end;
                        }

                    }

                }
            }
            group(CreateNewPackageTransferActionGroup2)
            {
                Caption = 'COMB';
                ShowCaption = false;
                cuegroup(CreateNewPackageTransferCueGroup2)
                {
                    Caption = 'COMB';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateNewPackageTransfer2)
                        {
                            Caption = 'COMB', Comment = 'TARAK';

                            // CaptionML = TRK = 'TARAK', ENU = 'TARAK';
                            Image = TileOrange;
                            // RunObject = page "Process Quality Control Doc.";
                            ToolTip = 'Executes the Approve Create New Package Transfer action.';
                            trigger OnAction()
                            var
                                Location: Record Location;
                                PackageTransferHeader: Record "Package Transfer Header IPK";
                            begin
                                Location.SetRange("Role Center Button Order IPK", 2);

                                Location.FindFirst();
                                PackageTransferHeader.Init();
                                PackageTransferHeader.Insert(true);
                                PackageTransferHeader.Validate("Transfer-to Code", Location.Code);
                                PackageTransferHeader.Modify(true);
                                Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);
                            end;
                        }

                    }

                }
            }
            group(CreateNewPackageTransferActionGroup3)
            {
                Caption = 'Q-TACK';
                ShowCaption = false;
                cuegroup(CreateNewPackageTransferCueGroup3)
                {
                    Caption = 'Q-TACK';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateNewPackageTransfer3)
                        {
                            Caption = 'Q-TACK', Comment = 'KULAK ÇUBUĞU';

                            // CaptionML = TRK = 'KULAK ÇUBUĞU', ENU = 'KULAK ÇUBUĞU';
                            Image = TileCyan;
                            // RunObject = page "Process Quality Control Doc.";
                            ToolTip = 'Executes the Approve Q-TACK action.';
                            trigger OnAction()
                            var
                                Location: Record Location;
                                PackageTransferHeader: Record "Package Transfer Header IPK";
                            begin
                                Location.SetRange("Role Center Button Order IPK", 3);

                                Location.FindFirst();
                                PackageTransferHeader.Init();
                                PackageTransferHeader.Insert(true);
                                PackageTransferHeader.Validate("Transfer-to Code", Location.Code);
                                PackageTransferHeader.Modify(true);
                                Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);
                            end;
                        }

                    }

                }
            }
            group(CreateNewPackageTransferActionGroup4)
            {
                Caption = 'FUSE';
                ShowCaption = false;
                cuegroup(CreateNewPackageTransferCueGroup4)
                {
                    Caption = 'FUSE';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateNewPackageTransfer4)
                        {
                            Caption = 'FUSE', Comment = 'FİTİL';

                            // CaptionML = TRK = 'FİTİL', ENU = 'FİTİL';
                            Image = TileBlue;

                            // RunObject = page "Process Quality Control Doc.";
                            ToolTip = 'Executes the Approve Create New Package Transfer action.';
                            trigger OnAction()
                            var
                                Location: Record Location;
                                PackageTransferHeader: Record "Package Transfer Header IPK";
                            begin
                                Location.SetRange("Role Center Button Order IPK", 4);

                                Location.FindFirst();
                                PackageTransferHeader.Init();
                                PackageTransferHeader.Insert(true);
                                PackageTransferHeader.Validate("Transfer-to Code", Location.Code);
                                PackageTransferHeader.Modify(true);
                                Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);
                            end;
                        }

                    }

                }
            }
            group(CreateNewPackageTransferActionGroup5)
            {
                Caption = 'WATERJET';
                ShowCaption = false;
                cuegroup(CreateNewPackageTransferCueGroup5)
                {
                    Caption = 'WATERJET';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateNewPackageTransfer5)
                        {
                            Caption = 'WATERJET', Comment = 'WATERJET';

                            // CaptionML = TRK = 'FİTİL', ENU = 'FİTİL';
                            Image = TileBlue;

                            // RunObject = page "Process Quality Control Doc.";
                            ToolTip = 'Executes the Approve Create New Package Transfer action.';
                            trigger OnAction()
                            var
                                Location: Record Location;
                                PackageTransferHeader: Record "Package Transfer Header IPK";
                            begin
                                Location.SetRange("Role Center Button Order IPK", 5);

                                Location.FindFirst();
                                PackageTransferHeader.Init();
                                PackageTransferHeader.Insert(true);
                                PackageTransferHeader.Validate("Transfer-to Code", Location.Code);
                                PackageTransferHeader.Modify(true);
                                Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);
                            end;
                        }

                    }

                }

            }
            group(CreateNewPackageTransferActionGroup6)
            {
                Caption = 'PONPON';
                ShowCaption = false;
                cuegroup(CreateNewPackageTransferCueGroup6)
                {
                    Caption = 'PONPON';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateNewPackageTransfer6)
                        {
                            Caption = 'PONPON', Comment = 'PONPON';

                            // CaptionML = TRK = 'FİTİL', ENU = 'FİTİL';
                            Image = TileBlue;

                            // RunObject = page "Process Quality Control Doc.";
                            ToolTip = 'Executes the Approve Create New Package Transfer action.';
                            trigger OnAction()
                            var
                                Location: Record Location;
                                PackageTransferHeader: Record "Package Transfer Header IPK";
                            begin
                                Location.SetRange("Role Center Button Order IPK", 6);

                                Location.FindFirst();
                                PackageTransferHeader.Init();
                                PackageTransferHeader.Insert(true);
                                PackageTransferHeader.Validate("Transfer-to Code", Location.Code);
                                PackageTransferHeader.Modify(true);
                                Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);
                            end;
                        }

                    }

                }
            }
            group(CreateNewPackageTransferActionGroup7)
            {
                Caption = 'DEPO SEVK';
                ShowCaption = false;
                cuegroup(CreateNewPackageTransferCueGroup7)
                {
                    Caption = 'DEPO SEVK';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateNewPackageTransfer7)
                        {
                            Caption = 'DEPO SEVK', Comment = 'DEPO SEVK';
                            Image = TileBlue;
                            ToolTip = 'Executes the Approve Create New Package Transfer action.';
                            trigger OnAction()
                            var
                                Location: Record Location;
                                PackageTransferHeader: Record "Package Transfer Header IPK";
                            begin
                                Location.SetRange("Role Center Button Order IPK", 7);

                                Location.FindFirst();
                                PackageTransferHeader.Init();
                                PackageTransferHeader.Insert(true);
                                PackageTransferHeader.Validate("Transfer-to Code", Location.Code);
                                PackageTransferHeader.Modify(true);
                                Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);
                            end;
                        }

                    }

                }
            }
            group(ItemsByLocationGroup)
            {
                ShowCaption = false;
                cuegroup(ItemsByLocationCue)
                {
                    Caption = 'Activities';
                    ShowCaption = false;
                    actions
                    {
                        action(ItemsByLocation)
                        {
                            Caption = 'Items By Location', Comment = 'Konuma Göre Maddeler';
                            // CaptionML = TRK = 'Palet Oluştur', ENU = 'Create Pallet';
                            Image = TileGreen;
                            // RunObject = page "Production Order List";
                            ToolTip = 'Executes the Items By Location action.';
                            trigger OnAction()
                            begin
                                Page.Run(Page::"Items by Location");
                            end;
                        }

                    }

                }
            }
            group(Lists)
            {
                Caption = 'Lists';
                ShowCaption = false;
                group(ProcessedWarehouseRecipt)
                {
                    Caption = 'Processed Warehouse Recipt';
                    ShowCaption = false;
                    cuegroup(ProcessedWarehouseReciptCueGroup)
                    {
                        Caption = 'Processed Warehouse Recipt';
                        ShowCaption = true;
                        field("Planned Production Orders"; Rec."Processed Warehouse Recipt")
                        {
                            DrillDownPageId = "Warehouse Receipts";
                            ToolTip = 'Specifies the value of the Processed Warehouse Recipt field.';
                        }
                    }
                }
                group(WaitingPackageTransfersGroup)
                {
                    Caption = 'Waiting Package Transfers';
                    ShowCaption = false;
                    cuegroup(WaitingPackageTransfersCueGroup)
                    {
                        Caption = 'Waiting Package Transfers';
                        ShowCaption = true;
                        field(WaitingPackageTransfers; Rec."Waiting Package Transfers")
                        {
                            DrillDownPageId = "Package Transfer Orders IPK";
                            ToolTip = 'Specifies the value of the Waiting Package Transfers field.';
                        }
                    }
                }
                group(ReadyToPostPackageTransfersGroup)
                {
                    Caption = 'Ready To Post Package Transfers';
                    ShowCaption = false;
                    cuegroup(ReadyToPostPackageTransfersCueGroup)
                    {
                        Caption = 'Ready To Post Package Transfers';
                        ShowCaption = true;
                        field(ReadyToPostPackageTransfers; Rec."Rdy. To Post Package Transfers")
                        {
                            DrillDownPageId = "Package Transfer Orders IPK";
                            ToolTip = 'Specifies the value of the Waiting Package Transfers field.';
                        }
                    }
                }
                group(ReadyToCombinePackageTransfersGroup)
                {
                    Caption = 'Ready To Combine Package Transfers';
                    ShowCaption = false;
                    cuegroup(ReadyToCombinePackageTransfersCueGroup)
                    {
                        Caption = 'Ready To Combine Package Transfers';
                        ShowCaption = true;
                        field("Ready-to Combine"; Rec."Ready-to Combine")
                        {
                        }
                    }
                }
            }
        }
    }

    trigger OnOpenPage()
    var
        UserSetup: Record "User Setup";
        UserText: Text[50];
    begin
        UserText := CopyStr(UserId(), 1, MaxStrLen(UserText));
        Rec.SetFilter("User ID Filter", UserText);
        UserSetup.Get(UserText);
        Rec.SetFilter("Waiting Pac. Tran. Loc. Filt.", UserSetup."Role Center Loc. Filter IPK");
    end;

    trigger OnAfterGetRecord()
    begin
        // Rec.CalcFields("Waiting Pac. Tran. Loc. Filt.");
    end;
}