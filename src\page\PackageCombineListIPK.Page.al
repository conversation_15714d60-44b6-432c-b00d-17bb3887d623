page 60039 "Package Combine List IPK"
{
    ApplicationArea = All;
    Caption = 'Package Combine List';
    PageType = List;
    SourceTable = "Package Combine Header IPK";
    UsageCategory = Lists;
    CardPageId = "Package Combine IPK";
    Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Item No. Filter"; Rec."Item No. Filter")
                {
                }
                field("Variant Code Filter"; Rec."Variant Code Filter")
                {
                }
                field("Location Code Filter"; Rec."Location Code Filter")
                {
                }
                field("Operation Date-Time"; Rec."Operation Date-Time")
                {
                }
                field("New Package Quantity"; Rec."New Package Quantity")
                {
                }
            }
        }
    }
}