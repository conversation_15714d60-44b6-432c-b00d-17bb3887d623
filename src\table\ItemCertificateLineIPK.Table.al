table 60007 "Item Certificate Line IPK"
{
    Caption = 'Item Certificate';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Certificate Line IPK";
    LookupPageId = "Item Certificate Line IPK";
    fields
    {

        field(1; "Item No."; Code[20])
        {
            Caption = 'No.';
            AllowInCustomizations = Always;
        }
        field(2; "Certificate No."; Code[20])
        {
            Caption = 'Certificate No.';
            TableRelation = "Item Certificate IPK";
            ToolTip = 'Specifies the value of the Table Name field.';
            // trigger OnValidate()
            // var
            //     "Item Certificates IPK": Record "Item Certificate IPK";
            // begin
            //     if "Certificate No." = '' then
            //         Init()
            //     else begin
            //         "Item Certificates IPK".Get("Certificate No.");
            //         "Certificate Authority" := "Item Certificates IPK"."Certificate Authority";
            //         "Item Type" := "Item Certificates IPK"."Item Type";
            //         "Certificate ID" := "Item Certificates IPK"."Certificate ID";
            //     end;
            // end;
        }
        field(3; "Certificate Authority"; Code[10])
        {
            Caption = 'Certificate Authority';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK"."Certificate Authority" where("No." = field("Certificate No.")));
            ToolTip = 'Specifies the value of the Certificate Authority field.';
        }
        field(4; "Item Type"; Code[10])
        {
            Caption = 'Item Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK"."Item Type" where("No." = field("Certificate No.")));
            ToolTip = 'Specifies the value of the Item Type field.';
        }
        field(5; "Certificate ID"; Code[100])
        {
            Caption = 'Certificate ID';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Certificate IPK"."Certificate ID" where("No." = field("Certificate No.")));
            ToolTip = 'Specifies the value of the Certificate ID field.';
        }
    }
    keys
    {
        key(Key1; "Item No.", "Certificate No.")
        {
            Clustered = true;
        }
    }
}