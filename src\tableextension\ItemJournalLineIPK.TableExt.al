tableextension 60015 "Item Journal Line IPK" extends "Item Journal Line"
{
    fields
    {
        field(60000; "Palette Barcode IPK"; Code[50])
        {
            Caption = 'Palette Barcode';
            ToolTip = 'Specifies the value of the Palette Barcode field.';
        }
        field(60001; "Palette Count IPK"; Integer)
        {
            Caption = 'Palette Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package No. Information" where("Package No." = field("Palette Barcode IPK")));
            ToolTip = 'Specifies the value of the Palette Count field.';
        }
        field(60002; "Total Qty. on Location IPK"; Decimal)
        {
            Caption = 'Total Quantity on Location';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Location Code" = field("Location Code"), Open = const(true)));
            ToolTip = 'Specifies the value of the Total Quantity on Location field.';
        }
        field(60003; "Physical Quantity IPK"; Decimal)
        {
            Caption = 'Physical Quantity';
            ToolTip = 'Specifies the value of the Physical Quantity field.';
            trigger OnValidate()
            var
                PhysicalQuantityExceedsTotalErr: Label 'Physical Quantity: %1 cannot be greater then Total Quantity on Location: %2', Comment = '%1="Item Journal Line"."Total Qty. on Location IPK"; %2="Item Journal Line"."Physical Quantity IPK"';
            begin
                if Rec."Total Qty. on Location IPK" < Rec."Physical Quantity IPK" then
                    Error(PhysicalQuantityExceedsTotalErr, Rec."Physical Quantity IPK", Rec."Total Qty. on Location IPK");
                // Rec.CalcFields("Total Qty. on Location IPK");
                Rec.Validate(Quantity, "Total Qty. on Location IPK" - Rec."Physical Quantity IPK");
            end;
        }
        // field(60002; "Difference Positive IPK"; Decimal)
        // {
        //     Caption = 'Difference Positive';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Item Journal Line Dtl. IPK".Quanity where("Journal Template Name" = field("Journal Template Name"), "Journal Batch Name" = field("Journal Batch Name"), "Journal Line No." = field("Line No."), Quanity = filter(> 0)));
        // }
        // field(60003; "Difference Negative IPK"; Decimal)
        // {
        //     Caption = 'Difference Negative';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Item Journal Line Dtl. IPK".Quanity where("Journal Template Name" = field("Journal Template Name"), "Journal Batch Name" = field("Journal Batch Name"), "Journal Line No." = field("Line No."), Quanity = filter(< 0)));
        // }

    }
}