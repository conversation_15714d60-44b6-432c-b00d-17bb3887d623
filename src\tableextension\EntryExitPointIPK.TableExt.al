tableextension 60039 "Entry/Exit Point IPK" extends "Entry/Exit Point"
{
    fields
    {
        field(60000; "Exit Point IPK"; Code[10])
        {
            Caption = 'Exit Point IPK';
            TableRelation = "Entry/Exit Point".Code;
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the "Exit Point IPK" field.';
        }
    }
    fieldgroups
    {
        addlast(DropDown; Code, Description, "Exit Point IPK") { }
    }
}