pageextension 60067 "E-Invoices INF IPK" extends "E-Invoices INF"
{
    layout
    {
        addbefore("E-Invoice No.")
        {

            field("Export No. IPK"; Rec."Export No. IPK")
            {
                ApplicationArea = All;
            }
            field("Document Date IPK"; Rec."Document Date IPK")
            {
                ApplicationArea = All;
            }
            field("B Customer Name IPK"; Rec."B Customer Name")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Buyer Customer Name field.';
            }

            // field("Customer Name IPK"; Rec."Customer Name IPK")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Customer Name field.';
            // }

            field("MCT Registration No. IPK"; Rec."MCT Registration No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the MCT Registration No. field.';
            }
            field("MCT Registration Date IPK"; Rec."MCT Registration Date IPK")
            {
                ApplicationArea = All;
            }
            field("Currency Code IPK"; Rec."Currency Code IPK")
            {
                ApplicationArea = All;
            }
            field("TL Amount IPK"; Rec."TL Amount IPK")
            {
                ApplicationArea = All;
            }

        }
    }
    actions
    {
        addafter(Comments)
        {
            action("Update E-Document IPK")
            {
                ApplicationArea = all;
                Caption = 'Update E-Document';
                Image = ReleaseDoc;
                ToolTip = 'Executes the Update E-Document action.';
                trigger OnAction()
                var
                    // EInvoiceHeader: Record "E-Invoice Header INF";
                    SalesInvoiceHeader: Record "Sales Invoice Header";
                    IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
                begin
                    SetSelectionFilter(Rec);
                    Rec.FindSet();
                    repeat
                        SalesInvoiceHeader.Get(Rec."No.");

                        Rec.Validate("Posting Date", SalesInvoiceHeader."Posting Date");
                        Rec.Validate("Issue Date", SalesInvoiceHeader."Posting Date");


                        IpekSalesManagement.SetDocumentCurrencyCode(SalesInvoiceHeader, Rec);
                        Rec.Modify(true);
                    until Rec.Next() = 0;
                    Message('Documents Updated !');
                    Rec.Reset();
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    var
    begin
        Rec."TL Amount IPK" := Rec."Tax Inclusive Total Amount" * Rec."Currency Exchange Rate";
    end;
}