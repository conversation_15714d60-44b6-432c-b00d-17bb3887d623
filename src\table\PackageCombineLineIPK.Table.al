table 60023 "Package Combine Line IPK"
{
    Caption = 'Package Combine Line';
    DataClassification = CustomerContent;
    DrillDownPageId = "Package Combine Subpage IPK";
    LookupPageId = "Package Combine Subpage IPK";
    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }

        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                Item.Get(Rec."Item No.");
                Rec.Description := Item.Description;
            end;
        }
        field(4; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(5; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(6; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(7; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(8; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
            AllowInCustomizations = Always;
        }
        field(9; "Quantity-to Add"; Decimal)
        {
            Caption = 'Quantity-to Add';
            MinValue = 0;
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the value of the Quantity-to Add field.';
        }
        // field(10; "Item Category Description"; Text[100])
        // {
        //     Editable = false;
        //     Caption = 'Item Category Description';
        //     ToolTip = 'Specifies the value of the Item Category Description field.';
        //     FieldClass = FlowField;
        //     CalcFormula = lookup(Item."Item Category Description IPK" where("No." = field("Item No.")));//alper
        // }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
            // SumIndexFields = "Quantity-to Add";
        }
        key(key2; "Quantity-to Add")
        {
            // SumIndexFields = "Quantity-to Add";
        }

    }
    trigger OnInsert()
    var
        PackageCombineLine: Record "Package Combine Line IPK";
    begin
        PackageCombineLine.SetRange("Document No.", Rec."Document No.");
        if PackageCombineLine.FindLast() then
            Rec."Line No." := PackageCombineLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}