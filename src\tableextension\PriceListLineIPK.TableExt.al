tableextension 60043 "Price List Line IPK" extends "Price List Line"
{
    fields
    {
        modify("Unit Price")
        {
            trigger OnAfterValidate()
            var
            begin
                Rec.CalcFields("Units Per Parcel IPK");//Todo: create a procedure for both of these fields
                if Rec."Units Per Parcel IPK" = 0 then
                    Rec."Piece Unit Price IPK" := Rec."Unit Price" // 1
                else
                    Rec."Piece Unit Price IPK" := Rec."Unit Price" / Rec."Units Per Parcel IPK";
            end;
        }
        field(60000; "Piece Unit Price IPK"; Decimal)
        {
            Caption = 'Piece Unit Price';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the price per piece unit for the selected product.';
            trigger OnValidate()
            var
            begin
                Rec.CalcFields("Units Per Parcel IPK");
                if Rec."Units Per Parcel IPK" = 0 then
                    Rec."Piece Unit Price IPK" := "Unit Price" // 1
                else
                    Rec."Unit Price" := Rec."Piece Unit Price IPK" * Rec."Units Per Parcel IPK";
            end;
        }
        field(60001; "Units Per Parcel IPK"; Decimal)
        {
            Caption = 'Units Per Parcel';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Units per Parcel" where("No." = field("Product No.")));
            ToolTip = 'Specifies the number of units contained in each parcel for the selected product.';
        }
    }
}