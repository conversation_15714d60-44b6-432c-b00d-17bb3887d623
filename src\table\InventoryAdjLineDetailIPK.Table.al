table 60033 "Inventory Adj. Line Detail IPK"
{
    Caption = 'Inventory Adj. Line Detail';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
        }
        field(4; "Package No."; Code[50])
        {
            Caption = 'Package No.';
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
        }
        field(6; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
        }
        field(7; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
        }
        field(8; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
        }
        field(9; "Line Quantity"; Decimal)
        {
            Caption = 'Line Quantity';
        }
        field(10; "Line Physical Quantity"; Decimal)
        {
            Caption = 'Line Physical Quantity';
        }
        field(11; "Difference Quantity"; Decimal)
        {
            Caption = 'Difference Quantity';
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        InventoryAdjLineDetail2: Record "Inventory Adj. Line Detail IPK";
    begin
        InventoryAdjLineDetail2.SetRange("Document No.", Rec."Document No.");
        InventoryAdjLineDetail2.SetRange("Document Line No.", Rec."Document Line No.");
        if InventoryAdjLineDetail2.FindLast() then
            Rec."Line No." := InventoryAdjLineDetail2."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}