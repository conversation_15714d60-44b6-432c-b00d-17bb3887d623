page 60057 "Production BOM Lines IPK"
{
    ApplicationArea = All;
    Caption = 'Production BOM Lines';
    PageType = List;
    SourceTable = "Production BOM Line";
    UsageCategory = Lists;
    Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Production BOM No."; Rec."Production BOM No.")
                {
                    ToolTip = 'Specifies the value of the Production BOM No. field.';
                }
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the number of the involved entry or record, according to the specified number series.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies a description of the production BOM line.';
                }

                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the value of the Quantity field.';
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    ToolTip = 'Specifies the variant of the item on the line.';
                }
                field(Width; ItemWidth)
                {
                    Caption = 'Item Width';
                    ToolTip = 'Specifies the width of one item unit when measured in the specified unit of measure.';
                }
                field(Length; ItemLength)
                {
                    Caption = 'Item Length';
                    ToolTip = 'Specifies the length of one item unit when measured in the specified unit of measure.';
                }
                field(Height; ItemHeight)
                {
                    Caption = 'Item Height';
                    ToolTip = 'Specifies the depth of one item unit when measured in the specified unit of measure.';
                }
                field(ItemWeight; ItemWeight)
                {
                    Caption = 'Item Weight';
                    ToolTip = 'Specifies the weight of one item unit when measured in the specified unit of measure.';
                }
                field("Quantity per"; Rec."Quantity per")
                {
                    ToolTip = 'Specifies how many units of the component are required to produce the parent item.';
                }
                field("Item Category Code IPK"; Rec."Item Category Code IPK")
                {
                }
                field("Item Category Description IPK"; Rec."Item Category Description IPK")
                {
                }
            }


        }
    }
    trigger OnAfterGetRecord()
    var
        ItemUnitofMeasure: Record "Item Unit of Measure";
    begin
        if ItemUnitofMeasure.Get(Rec."No.", 'ADET') then begin
            ItemLength := ItemUnitofMeasure.Length;
            ItemWidth := ItemUnitofMeasure.Width;
            ItemHeight := ItemUnitofMeasure.Height;
            ItemWeight := ItemUnitofMeasure.Weight;
        end;
    end;

    var
        ItemHeight: Decimal;
        ItemLength: Decimal;
        ItemWeight: Decimal;
        ItemWidth: Decimal;
}