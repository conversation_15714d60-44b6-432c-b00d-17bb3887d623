tableextension 60005 "Family Line IPK" extends "Family Line"
{
    fields
    {
        field(60000; "Variant Code IPK"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            var
                Item: Record Item;
                ItemVariant: Record "Item Variant";
            begin
                if ItemVariant.Get(Rec."Item No.", Rec."Variant Code IPK") then
                    Rec.Description := ItemVariant.Description
                else
                    if Item.Get(Rec."Item No.") then
                        Rec.Description := Item.Description;
            end;
        }
    }
}