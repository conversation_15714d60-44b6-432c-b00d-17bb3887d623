table 60006 "Package Creation IPK"
{
    Caption = 'Package Creation';
    DataClassification = CustomerContent;
    TableType = Temporary;
    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the document number.';
            trigger OnValidate()
            begin
                IpekBasicFunctions.PopulateItemNoFromDocumentNo(Rec);
            end;
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the line number within the document.';

            TableRelation = if ("Source Type" = const(Production)) "Prod. Order Line"."Line No." where("Prod. Order No." = field("Document No."), Status = const(Released));
            trigger OnValidate()
            var
                Item: Record Item;
                ProdOrderLine: Record "Prod. Order Line";
                IpekProductionManagement: Codeunit "Ipek Production Management IPK";
            begin
                case "Source Type" of
                    "Package Creation Source Type"::Production:
                        if ProdOrderLine.Get(ProdOrderLine.Status::Released, Rec."Document No.", Rec."Document Line No.") then begin
                            Item.Get(ProdOrderLine."Item No.");
                            Rec.Validate("Item No.", ProdOrderLine."Item No.");

                            Rec.Validate("Variant Code", ProdOrderLine."Variant Code");
                            Rec.Validate("Package Quantity", Item."Pieces on Pallet IPK");
                            Rec.Validate("Lot No.", IpekProductionManagement.GetLotNoFromProdOrderLotNos(ProdOrderLine));

                            Rec.Validate("Item Pieces Of Pallet", Item."Pieces on Pallet IPK");
                            Rec.Validate("Order Quantity", ProdOrderLine."Quantity (Base)");
                            Rec.Validate("Max Available Quantity", ProdOrderLine."Remaining Qty. (Base)");

                            // Rec.Validate();
                        end else begin
                            Rec.Validate("Item No.", '');
                            Rec.Validate("Variant Code", '');
                            Rec.Validate("Package Quantity", 0);
                            Rec.Validate("Lot No.", '');
                            Rec.Validate("Item Pieces Of Pallet", 0);
                            Rec.Validate("Order Quantity", 0);
                            Rec.Validate("Max Available Quantity", 0);
                        end;

                end;
            end;
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the item number.';
            trigger OnValidate()
            begin
                Rec."Item Description" := IpekBasicFunctions.GetItemDescriptionFromItemNo(Rec."Item No.");
            end;
        }
        field(4; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies a description of the item.';
        }
        field(5; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number.';
        }
        field(6; "Package Count"; Integer)
        {
            Caption = 'Package Count';
            ToolTip = 'Specifies the package count.';
        }
        field(7; "Package Quantity"; Decimal)
        {
            Caption = 'Package Quantity';
            ToolTip = 'Specifies the package quantity.';
        }
        field(8; "Creation Method"; Enum "Package Creation Method IPK")
        {
            Caption = 'Creation Method';
            ToolTip = 'Specifies the package creation method.';
            trigger OnValidate()
            begin
                case Rec."Creation Method" of
                    Rec."Creation Method"::Single:
                        begin
                            Rec.Validate("Package Count", 1);
                            Rec.Validate("Package Quantity", 0);
                        end;
                    Rec."Creation Method"::Multiple:
                        begin
                            Rec.Validate("Package Count", 0);
                            Rec.Validate("Package Quantity", 0);
                        end;
                end;
            end;
        }
        field(9; "Source Type"; Enum "Package Creation Source Type")
        {
            Caption = 'Source Type';
            ToolTip = 'Specifies the source type of the package creation.';
            Editable = false;
        }
        field(10; "Item Pieces Of Pallet"; Decimal)
        {
            Caption = 'PackageCreation';
            AllowInCustomizations = Always;
        }
        field(11; "Order Quantity"; Decimal)
        {
            Caption = 'Order Quantity';
            ToolTip = 'Specifies the value of the Order Quantity field.';
            DataClassification = ToBeClassified;
        }
        field(12; "Remaining Quantity"; Decimal)
        {
            Caption = 'Remaining Quantity';
            AllowInCustomizations = Always;
            DataClassification = ToBeClassified;
        }
        field(13; "Max Available Quantity"; Decimal)
        {
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Max Available Quantity field.';
            Caption = 'Max Available Quantity';
        }
        field(14; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            DataClassification = ToBeClassified;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            begin
                if "Variant Code" <> '' then
                    Rec."Item Description" := IpekBasicFunctions.GetItemDescriptionFromVariantCode(Rec."Item No.", Rec."Variant Code");
            end;
        }
        field(15; "Produced By"; Enum "Production Centers IPK")
        {
            Caption = 'Produced By';
            ToolTip = 'Specifies the value of the Produced By field.';
            InitValue = " ";
        }
        // field(15; "Total Package Quantity"; Decimal)
        // {
        //     DataClassification = ToBeClassified;
        //     AllowInCustomizations = Always;
        //     Caption = 'Total Package Quantity';
        //     ToolTip = 'Specifies the value of the Total Package Quantity field.';
        // }



    }
    keys
    {
        key(PK; "Document No.", "Document Line No.")
        {
            Clustered = true;
        }
    }
    var
        IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
}