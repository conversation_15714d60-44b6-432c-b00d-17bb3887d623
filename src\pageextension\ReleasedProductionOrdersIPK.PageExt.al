pageextension 60038 "Released Production Orders IPK" extends "Released Production Orders"
{
    layout
    {
        addafter(Quantity)
        {
            field("Remaining Quantity IPK"; Rec."Remaining Quantity IPK")
            {
                ApplicationArea = All;
            }
            field("Last Production Date-Time IPK"; Rec."Last Production Date-Time IPK")
            {
                ApplicationArea = All;
            }
            field("Package Count IPK"; Rec."Package Count IPK")
            {
                ApplicationArea = All;
            }

            field("Consumption Location Code IPK"; Rec."Consumption Location Code IPK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addafter("Change &Status")
        {
            action("UpdateLastProducedDates IPK")
            {
                Caption = 'Update Last Prod. Dates';
                ToolTip = 'Executes the Update Last Prod. Dates action.';
                Image = UpdateUnitCost;
                ApplicationArea = All;
                Visible = false;
                trigger OnAction()
                var
                    IpekProductionManagement: Codeunit "Ipek Production Management IPK";
                begin
                    IpekProductionManagement.UpdateHistoricLast();
                end;
            }
        }
    }
}