page 60011 "Warehouse Receipt Line Details"
{
    ApplicationArea = All;
    Caption = 'Warehouse Receipt Line Details';
    PageType = List;
    SourceTable = "Warehouse Receipt Line Dtl IPK";
    UsageCategory = Lists;
    InsertAllowed = false;
    // ModifyAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {


                field("Document No."; Rec."Document No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Document No. field.';
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Document Line No. field.';
                }
                field("Line No."; Rec."Line No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Line No. field.';
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }
                field("Package No."; Rec."Package No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Package No. field.';
                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.SetRange("Package No.", Rec."Package No.");
                        PackageNoInformation.FindFirst();

                        PageManagement.PageRun(PackageNoInformation);
                    end;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Item No. field.';
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Variant Code field.';
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Item Description field.';
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Quantity field.';
                }
                field("Item Tracking Info Assignd IPK"; Rec."Item Tracking Info Assignd IPK")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the Item Tracking Info. Assigned field.';
                }
                field(Recieved; Rec.Recieved)
                {
                    Editable = true;
                    ToolTip = 'Specifies the value of the Recieved field.';
                }

            }
        }
    }
    var
        PageManagement: Codeunit "Page Management";
}