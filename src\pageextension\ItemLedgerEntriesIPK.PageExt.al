pageextension 60034 "Item Ledger Entries IPK" extends "Item Ledger Entries"
{
    layout
    {
        modify("Lot No.")
        {
            Visible = true;
        }
        modify("Variant Code")
        {
            Visible = true;
        }
        addafter("Entry No.")
        {

        }
    }
    actions
    {
        addfirst("F&unctions")
        {
            action("ViewPalettes IPK")
            {
                ApplicationArea = All;
                Caption = 'View Palettes', Comment = 'TRK="Paletleri Görüntüle"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ViewDescription;
                ToolTip = 'Executes the View Palettes action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    PackageNoInformation.SetRange("Location Code IPK", Rec."Location Code");
                    PackageNoInformation.SetRange("Line Item No. IPK", Rec."Item No.");
                    PackageNoInformation.SetRange("Line Variant Code IPK", Rec."Variant Code");
                    PackageNoInformation.SetRange(Blocked, false);
                    PackageNoInformation.SetFilter("Total Quantity IPK", '>0');

                    PageManagement.PageRun(PackageNoInformation);
                end;
            }
        }
    }
    var
        PageManagement: Codeunit "Page Management";
}