tableextension 60016 "Location IPK" extends Location
{
    fields
    {
        field(60000; "Role Center Button Order IPK"; Integer)
        {
            Caption = 'Role Center Button Order';
            ToolTip = 'Specifies the value of the Role Center Button Order field.';
        }
        field(60001; "Production Location IPK"; Boolean)
        {
            Caption = 'Production Location';
            ToolTip = 'Specifies the value of the Production Location field.';
        }
        field(60002; "Visible On Items By LocMat IPK"; Boolean)
        {
            Caption = 'Visible On Items By Loc Matrix';
            ToolTip = 'Specifies the value of the Visible On Items By Loc Matrix field.';
        }
    }
    keys
    {
        key(Key4; "Role Center Button Order IPK")
        {
        }
    }
}