page 60021 "Process Quality Control Doc."
{
    ApplicationArea = All;
    Caption = 'Process Quality Control Doc.';
    PageType = NavigatePage;

    layout
    {
        area(Content)
        {
            field("Palette Barcode"; PaletteBarcode)
            {
                Caption = 'Label';
                ToolTip = 'Specifies the value of the Label field.';

                trigger OnValidate()
                var
                    // PackageNoInformation: Record "Package No. Information";
                    QualityControlHeaderQCM: Record "Quality Control Header QCM";
                    QualityControlHeaderQCM2: Record "Quality Control Header QCM";
                    QualityControlLineQCM: Record "Quality Control Line QCM";
                    ConfirmManagement: Codeunit "Confirm Management";
                begin
                    QualityControlHeaderQCM.SetRange("Package No.", PaletteBarcode);
                    QualityControlHeaderQCM.SetRange(Type, Enum::"Quality Control Type QCM"::Production);
                    QualityControlHeaderQCM.SetRange(Status, Enum::"Quality Control Status QCM"::"Input Pending");

                    QualityControlHeaderQCM.FindFirst();

                    if ConfirmManagement.GetResponse('Do you want to approve selected quality control document?') then begin
                        QualityControlLineQCM.SetRange("Document No.", QualityControlHeaderQCM."No.");
                        QualityControlLineQCM.FindSet();

                        repeat
                            QualityControlLineQCM.TestField("Specification Reference", Enum::"Q.C. Spec. Reference Type QCM"::Selection);
                            QualityControlLineQCM.Validate("Selection Result Value", Enum::"Quality Control Selection QCM"::OK);
                            QualityControlLineQCM.Modify(true);
                        until QualityControlLineQCM.Next() = 0;
                    end else begin
                        // PageManagement.PageRun(QualityControlHeaderQCM);
                        QualityControlHeaderQCM2 := QualityControlHeaderQCM;
                        Page.RunModal(Page::"Quality Control QCM", QualityControlHeaderQCM2);
                    end;
                    PaletteBarcode := '';
                end;
            }
        }
    }
    var
        // PageManagement: Codeunit "Page Management";
        PaletteBarcode: Code[50];
}