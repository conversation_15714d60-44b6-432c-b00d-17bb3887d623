pageextension 60000 "Item Card IPK" extends "Item Card"
{
    layout
    {
        addlast(content)
        {
            group("Ipek Pamuk IPK")
            {
                Caption = 'Ipek Pamuk', Locked = true;

                field("Brand IPK"; Rec."Brand IPK")
                {
                    ApplicationArea = All;
                }
                field("Customer No. IPK"; Rec."Customer No. IPK")
                {
                    ApplicationArea = All;
                }
                field("Customer Name IPK"; Rec."Customer Name IPK")
                {
                    ApplicationArea = All;
                }
                field("Pieces on Pallet IPK"; Rec."Pieces on Pallet IPK")
                {
                    ApplicationArea = All;
                }
                field("Units per Parcel IPK"; Rec."Units per Parcel")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Units per Parcel field.';
                }
                field("Export/Domestic IPK"; Rec."Export/Domestic IPK")
                {
                    ApplicationArea = All;
                }
                field("Default Output Location IPK"; Rec."Default Output Location IPK")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
    actions
    {
        addafter(ApplyTemplate)
        {

            action("Certificates IPK")
            {
                ApplicationArea = All;
                Caption = 'Certificates';
                Image = Agreement;
                ToolTip = 'Shows Certificates For This Item.';
                RunObject = page "Item Certificate Line IPK";
                RunPageLink = "Item No." = field("No.");
            }
            action("ReCalculateItemVolumes IPK")
            {
                ApplicationArea = All;
                Caption = 'ReCalculate All Item Volumes';
                Image = Agreement;
                ToolTip = 'Shows ReCalculate All Item Volumes.';
                trigger OnAction()
                var
                    Item: Record Item;
                    ItemUnitofMeasure: Record "Item Unit of Measure";
                begin
                    Item.FindSet();
                    repeat
                        ItemUnitofMeasure.SetRange("Item No.", Item."No.");
                        ItemUnitofMeasure.SetRange(Code, Item."Base Unit of Measure");
                        if ItemUnitofMeasure.FindFirst() then begin
                            Item.Validate("Unit Volume", ItemUnitofMeasure.Cubage);
                            Item.Modify(true);
                        end;
                    until Item.Next() = 0;
                end;
            }
        }
    }
}