tableextension 60007 "Sales Line IPK" extends "Sales Line"
{
    fields
    {
        field(60000; "Warehouse Qty. to Ship IPK"; Decimal)
        {
            Caption = 'Warehouse Qty. to Ship';
            DecimalPlaces = 0 : 5;
            BlankZero = true;
            ToolTip = 'Specifies the value of the Warehouse Qty. to Ship field.';
            trigger OnValidate()
            begin
                "Warehouse Qty. to Ship IPK" := UnitofMeasureManagement.RoundAndValidateQty("Warehouse Qty. to Ship IPK", "Qty. Rounding Precision", FieldCaption("Warehouse Qty. to Ship IPK"));

                if "Warehouse Qty. to Ship IPK" > Rec."Outstanding Quantity" then
                    Error(CannotShipErrorInfo());
            end;
        }
        field(60001; "Line Net Weight IPK"; Decimal)
        {
            Caption = 'Line Net Weight';
            ToolTip = 'Specifies the value of the Line Net Weight field.';
            Editable = false;
        }
        field(60002; "Line Gross Weight IPK"; Decimal)
        {
            Caption = 'Line Gross Weight';
            ToolTip = 'Specifies the value of the Line Gross Weight field.';
            Editable = false;
        }
        field(60003; "Line Volume IPK"; Decimal)
        {
            Caption = 'Line Volume';
            ToolTip = 'Specifies the value of the Line Volume field.';
            Editable = false;
        }
        field(60004; "Line Unit Quantity IPK"; Integer)
        {
            Caption = 'Line Unit Quantity';
            Editable = true;
            ToolTip = 'Specifies the value of the Line Unit Quantity field.';
            trigger OnValidate()
            var
                LineUnitQuantityErr: Label 'Line unit quantity must be a order of This Lines Units per Parcel you should select  %1 or %2', Comment = '%1=; %2=';
            begin
                if (Rec."Line Unit Quantity IPK" mod Rec."Units per Parcel") = 0 then
                    Rec.Quantity := Rec."Line Unit Quantity IPK" / Rec."Units per Parcel" else
                    Error(LineUnitQuantityErr, "Line Unit Quantity IPK" - (Rec."Line Unit Quantity IPK" mod Rec."Units per Parcel"), "Line Unit Quantity IPK" - (Rec."Line Unit Quantity IPK" mod Rec."Units per Parcel") + Rec."Units per Parcel");
            end;
        }
        field(60005; "FOB Piece Unit Price IPK"; Decimal)
        {
            Caption = 'FOB Piece Unit Price';
            ToolTip = 'Specifies the value of the FOB Piece Unit Price field.';
            AutoFormatExpression = Rec."Currency Code";
            Editable = false;
            AutoFormatType = 2;
            trigger OnValidate()
            begin
                Rec.Validate("FOB Line Amount IPK", Rec."FOB Piece Unit Price IPK" * Rec."Units per Parcel" * Rec.Quantity);
            end;
        }
        field(60006; "Freight Inc PieceUnitPrice IPK"; Decimal)
        {
            Caption = 'Freight Included Piece Unit Price';
            ToolTip = 'Specifies the value of the CFR Piece Unit Price field.';
            AutoFormatExpression = Rec."Currency Code";
            AutoFormatType = 2;

            trigger OnValidate()
            begin

                Rec.Validate("Q/O Unit Price INF", Rec."Freight Inc PieceUnitPrice IPK" * Rec."Units per Parcel");
            end;
        }
        field(60007; "FOB Line Amount IPK"; Decimal)
        {
            Caption = 'FOB Line Amount';
            ToolTip = 'Specifies the value of the FOB Line Amount field.';
            AutoFormatExpression = Rec."Currency Code";
            AutoFormatType = 2;
            Editable = false;
            DecimalPlaces = 0 : 2;
        }





        modify(Quantity)
        {
            trigger OnAfterValidate()
            begin
                "Line Net Weight IPK" := Rec."Net Weight" * Rec.Quantity;
                "Line Gross Weight IPK" := Rec."Gross Weight" * Rec.Quantity;
                "Line Volume IPK" := Rec."Unit Volume" * Rec.Quantity;
                if Rec."Units per Parcel" < 1 then
                    Rec."Units per Parcel" := 1;

                Rec."Line Unit Quantity IPK" := Rec."Units per Parcel" * Rec.Quantity;
                //  if Rec."Units per Parcel" mod 1 = 0 then
                //     Rec."Units per Parcel" := Round(Rec."Units per Parcel", 1);
                // if Rec.Type = Rec.Type::Item then begin
                //     Item.Get(Rec."No.");
                //     if Rec."Qty. per Unit of Measure" = 1 then


                //     else
                //         "Line Unit Quantity IPK" := Rec.Quantity;
                // end;
            end;
        }
        modify("Gross Weight")
        {
            trigger OnAfterValidate()
            begin
                "Line Net Weight IPK" := Rec."Net Weight" * Rec.Quantity;
                "Line Gross Weight IPK" := Rec."Gross Weight" * Rec.Quantity;
                "Line Volume IPK" := Rec."Unit Volume" * Rec.Quantity;
                "Line Unit Quantity IPK" := Rec."Units per Parcel" * Rec.Quantity;
            end;
        }
        modify("Net Weight")
        {
            trigger OnAfterValidate()
            begin
                "Line Net Weight IPK" := Rec."Net Weight" * Rec.Quantity;
                "Line Gross Weight IPK" := Rec."Gross Weight" * Rec.Quantity;
                "Line Volume IPK" := Rec."Unit Volume" * Rec.Quantity;
                "Line Unit Quantity IPK" := Rec."Units per Parcel" * Rec.Quantity;
            end;
        }
        // modify("Unit of Measure Code")
        // {
        //     trigger OnAfterValidate()
        //     var
        //         Item: Record Item;
        //         ItemUnitofMeasure: Record "Item Unit of Measure";
        //     begin
        //         // ItemUnitofMeasure.Get()
        //         "Line Net Weight IPK" := Rec."Net Weight" * Rec.Quantity;
        //         "Line Gross Weight IPK" := Rec."Gross Weight" * Rec.Quantity;
        //         "Line Volume IPK" := Rec."Unit Volume" * Rec.Quantity;
        //         "Line Unit Quantity IPK" := Rec."Units per Parcel" * Rec.Quantity;
        //     end;
        // }
    }
    local procedure CannotShipErrorInfo(): ErrorInfo
    var
        ErrorMesageManagement: Codeunit "Error Message Management";
    begin
        exit(ErrorMesageManagement.BuildActionableErrorInfo(
            WarehouseQtyShipNotValidTitleLbl,
            StrSubstNo(YouCanNotShipThatManyUnitsErr, "Outstanding Quantity"),
            Rec.RecordId(),
            StrSubstNo(QtyShipActionLbl, "Outstanding Quantity"),
            Codeunit::"Ipek Sales Management IPK",
            'SetSaleWarehouseShipQty',
            StrSubstNo(QtyShipActionDescriptionLbl, Rec.FieldCaption("Warehouse Qty. to Ship IPK"), Rec.Quantity)));
    end;

    var
        UnitofMeasureManagement: Codeunit "Unit of Measure Management";
        QtyShipActionDescriptionLbl: Label 'Corrects %1 to %2', Comment = '%1 - Qty. to Ship field caption, %2 - Quantity';
        QtyShipActionLbl: Label 'Set value to %1', Comment = '%1=Qty. to Ship';
        WarehouseQtyShipNotValidTitleLbl: Label 'Warehouse Qty. to Ship isn''t valid';
        YouCanNotShipThatManyUnitsErr: Label 'You cannot ship more than %1 units.', Comment = '%1 - Outstanding Quantity';
}