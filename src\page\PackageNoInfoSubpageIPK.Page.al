page 60017 "Package No. Info. Subpage IPK"
{
    ApplicationArea = All;
    Caption = 'Package No. Info. Subpage';
    PageType = ListPart;
    SourceTable = "Package No. Info. Line IPK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Line No."; Rec."Line No.")
                {
                }
                field("Item No."; Rec."Line Item No.")
                {
                    Caption = 'Item No.';
                }
                field("Variant Code"; Rec."Line Variant Code")
                {
                    Caption = 'Variant Code';
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = Rec."Source Package No. IPK" = '';
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    ToolTip = 'Specifies the value of the Unit of Measure Code field.';
                }
                field("Lot No."; Rec."Lot No.")
                {
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }
                field("Source Package No. IPK"; Rec."Source Package No. IPK")
                {
                }
                // field("Location Code"; Rec."Location Code")
                // {
                //     ToolTip = 'Specifies the value of the Description field.';
                // }
                // field("Package No."; Rec."Package No.")
                // {
                //     ToolTip = 'Specifies the value of the Package No. field.';
                // }
            }
        }
    }
    actions
    {

    }
}