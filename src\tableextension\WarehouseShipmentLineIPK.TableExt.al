tableextension 60001 "Warehouse Shipment Line IPK" extends "Warehouse Shipment Line"
{
    fields
    {
        field(60000; "Unit Volume IPK"; Decimal)
        {
            Caption = 'Unit Volume';
            ToolTip = 'Specifies the value of the Unit Volume field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Volume IPK", Rec."Unit Volume IPK" * Rec.Quantity);
            end;
        }
        field(60001; "Line Volume IPK"; Decimal)
        {
            Caption = 'Line Volume';
            ToolTip = 'Specifies the value of the Line Volume field.';
        }
        field(60002; "Net Weight IPK"; Decimal)
        {
            Caption = 'Net Weight';
            ToolTip = 'Specifies the value of the Net Weight field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Net Weight IPK", Rec."Net Weight IPK" * Rec.Quantity);
            end;
        }
        field(60003; "Gross Weight IPK"; Decimal)
        {
            Caption = 'Gross Weight';
            ToolTip = 'Specifies the value of the Gross Weight field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Gross Weight IPK", Rec."Gross Weight IPK" * Rec.Quantity);
            end;
        }
        field(60004; "Units per Parcel IPK"; Decimal)
        {
            Caption = 'Units per Parcel';
            ToolTip = 'Specifies the value of the Units per Parcel field.';
            trigger OnValidate()
            begin
                Rec.Validate("Line Unit Quantity IPK", Rec."Units per Parcel IPK" * Rec.Quantity);
            end;
        }
        field(60005; "Line Net Weight IPK"; Decimal)
        {
            Caption = 'Line Net Weight';
            ToolTip = 'Specifies the value of the Line Net Weight field.';
        }
        field(60006; "Line Gross Weight IPK"; Decimal)
        {
            Caption = 'Line Gross Weight';
            ToolTip = 'Specifies the value of the Line Gross Weight field.';
        }
        field(60007; "Line Unit Quantity IPK"; Integer)
        {
            Caption = 'Line Unit Quantity';
            Editable = false;
            ToolTip = 'Specifies the value of the Line Unit Quantity field.';
        }
        field(60008; "Item Reference No. IPK"; Code[50])
        {
            AccessByPermission = tabledata "Item Reference" = R;
            Caption = 'Item Reference No.';
            ExtendedDatatype = Barcode;
            Editable = false;
            ToolTip = 'Specifies the value of the Item Reference No. field.';
        }
    }
}