report 60002 "ShipmentAdviceIpk IPK"
{
    ApplicationArea = All;
    Caption = 'ShipmentAdviceIpk';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        /*
        Shipment Advice : Load card Bitti
            Invoice Date: e-faturanın numarası gömüldüğü anda gönderildiği tarihde gömülcek(issue date efaturadan)
            Invoice no : e-fatura entegratore gönderildiği anda gömülecek
            .Shipment date: load carda eklenen posting date'den gelcek
            .Departure date: ambar irsaliyesinden gelcek ETS alanı
            .Arrival Date: ETA
            .Load Carda posting date ekeleneck,
            .Ship to yanlış, o bill to bilgileri olacak
            .alttakide bill to olacak
            .Satırlar:
                Madde no bazında gruplanacak
        */
        dataitem("Load Header IPK"; "Load Header IPK")
        {
            RequestFilterFields = "No.";
            column(TruckLicensePlate_LoadHeaderIPK; "Truck License Plate")
            {
            }
            column(TrailerLicensePlate_LoadHeaderIPK; "Trailer License Plate")
            {
            }
            column(Posting_Date; "Posting Date")
            {
            }
            dataitem("Warehouse Shipment Header"; "Warehouse Shipment Header")
            {
                DataItemLink = "No." = field("Warehouse Shipment No.");
                column(ETA_IPK; "ETA IPK")
                {
                }
                column(ETS_IPK; "ETS IPK")
                {
                }
                column(Vessel_Name_IPK; "Vessel Name IPK")
                {
                }
                column(Shipping_Agent_Code; "Shipping Agent Code")
                {
                }
                column(E_Export_No__IPK; "E-Export No. IPK")
                {
                }
                dataitem("Shipping Agent"; "Shipping Agent")
                {
                    DataItemLink = Code = field("Shipping Agent Code");
                    column(Name; Name)
                    {
                    }
                }
                dataitem("Ship-to Address"; "Ship-to Address")
                {
                    DataItemLink = Code = field("Ship-to Code INF");
                    column(Name_ShiptoAddress; Name)
                    {
                    }
                    column(Address_ShipToCustomer; Address)
                    {
                    }
                    column(Address2_ShipToCustomer; "Address 2")
                    {
                    }
                    column(City_ShipToCustomer; City)
                    {
                    }
                    column(County_ShipToCustomer; County)
                    {
                    }
                }
                dataitem("Sell-to Customer"; Customer)
                {
                    DataItemLink = "No." = field("Source No. INF");
                    column(Name_SelltoCustomer; Name)
                    {
                    }
                    column(Address_SelltoCustomer; Address)
                    {
                    }
                    column(Address2_SelltoCustomer; "Address 2")
                    {
                    }
                    column(City_SelltoCustomer; City)
                    {
                    }
                    column(County_SelltoCustomer; County)
                    {
                    }
                }

            }
            dataitem("Load Planning Line IPK"; "Load Planning Line IPK")
            {
                DataItemLink = "Warehouse Shipment No." = field("Warehouse Shipment No."), "Document No." = field("No.");

                column(ItemNo_LoadPlanningLineIPK; "Item No.")
                {
                }
                column(Description_LoadPlanningLineIPK; Description)
                {
                }

                column(QuantityLoaded_LoadPlanningLineIPK; "Quantity Loaded")
                {
                }
                column(LineGrossWeight_LoadPlanningLineIPK; "Line Gross Weight")
                {
                }
                column(LineNetWeight_LoadPlanningLineIPK; "Line Net Weight")
                {
                }

            }

        }
    }
    requestpage
    {
        layout
        {

        }
        actions
        {

        }
    }
    var
}