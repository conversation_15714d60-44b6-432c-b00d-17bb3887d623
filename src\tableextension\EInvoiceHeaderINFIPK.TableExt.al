tableextension 60038 "E-Invoice Header INF IPK" extends "E-Invoice Header INF"
{
    fields
    {
        field(60003; "Export No. IPK"; Integer)
        {
            Editable = false;
            Caption = 'Export No.';
            ToolTip = 'Specifies the value of the Export No. field.';
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Export No. IPK" where("No." = field("E-Invoice No.")));
        }
        field(60001; "Document Date IPK"; Date)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Document Date" where("No." = field("E-Invoice No.")));
            Caption = 'Document Date';
            ToolTip = 'Specifies the value of the Document Date field.';
        }
        // field(60002; "Customer Name IPK"; Text[100])
        // {
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Sales Invoice Header"."Sell-to Customer Name" where("No." = field("E-Invoice No.")));
        //     Caption = 'Customer Name';
        //     ToolTip = 'Specifies the value of the DCustomer Name field.';
        // }
        field(60004; "MCT Registration Date IPK"; Date)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Document Date" where("No." = field("E-Invoice No.")));
            Caption = 'MCT Registration Date';
            ToolTip = 'Specifies the value of the MCT Registration Date field.';
        }
        field(60005; "Currency Code IPK"; Code[100])
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Currency Code" where("No." = field("E-Invoice No.")));
            Caption = 'Currency Code';
            ToolTip = 'Specifies the value of the Currency Code field.';
        }
        field(60006; "TL Amount IPK"; Decimal)
        {
            Caption = 'TL Amount';
            ToolTip = 'Specifies the value of the TL Amount field.';
        }

    }
}