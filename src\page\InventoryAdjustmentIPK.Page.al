page 60045 "Inventory Adjustment IPK"
{
    ApplicationArea = All;
    Caption = 'Inventory Adjustment';
    PageType = Document;
    SourceTable = "Inventory Adjustment Header";
    UsageCategory = Lists;
    Editable = true;
    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus IPK")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                end;
            }

            group(Header)
            {
                Caption = 'Header';
                ShowCaption = false;
                // Editable = not Rec.Posted;
                field("No."; Rec."No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                // field("Item No."; Rec."Item No.")
                // {
                // }
                field("Journal Template Name"; Rec."Journal Template Name")
                {
                }
                field("Target Batch Name"; Rec."Journal Batch Name")
                {
                }
                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies the status of the inventory adjustment.';
                }
                field("Transfer Order No."; Rec."Transfer Order No.")
                {
                    ToolTip = 'Specifies the value of the Transfer Order No. field.';
                    trigger OnDrillDown()
                    var
                        PackageTransferHeader: Record "Package Transfer Header IPK";
                    begin
                        if Rec."Transfer Order No." <> '' then begin
                            if PackageTransferHeader.Get(Rec."Transfer Order No.") then
                                PAGE.Run(PAGE::"Package Transfer Order IPK", PackageTransferHeader)
                            else
                                Message('Transfer Order not found.');
                        end else
                            Message('No Transfer Order No. available.');
                    end;
                }
                // field("Ready For Post"; Rec."Ready For Post")
                // {
                //     ToolTip = 'Specifies the value of the Pack Nos Modified field.';
                // }
                // field(Posted; Rec.Posted)
                // {
                // }

            }
            group("Barcode Area")
            {
                Caption = 'Barcode Area';
                ShowCaption = false;
                field(Barcode; Rec.Barcode)
                {
                    Caption = 'Barcode';
                    Editable = true;
                    QuickEntry = true;
                    trigger OnValidate()
                    begin


                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                        Rec.Barcode := '';
                    end;
                }
            }
            group("Package Detail")
            {
                Caption = 'Package Detail';
                ShowCaption = false;
                field(TotalQuantity; Rec.LastBarcodeTotalQuantity)
                {
                    Caption = 'Last Barcode Total Quantity';
                    Editable = false;
                    Enabled = false;
                }
                field(TotalPhysicalQuantity; Rec."LB Total Phy. Qty.")
                {
                    trigger OnValidate()
                    begin

                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                    end;
                }
                field("Read Package Quantity"; Rec."Read Package Quantity")
                {
                }
            }
            part(Line; "Inventory Adjustment Subpage")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                // Editable = not Rec.Posted;
            }
        }

    }
    actions
    {
        area(Processing)
        {
            action(FixPackageQuantities)
            {
                ApplicationArea = All;
                Caption = 'Fix Package Quantities';
                Enabled = not Rec.FixPackageQuantitiesPressed;
                Image = AdjustEntries;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Fix Package Quantities action.';

                trigger OnAction()
                begin
                    InventoryAdjustment.FixPackageQuantities(Rec);
                    Rec.Status := Rec.Status::Active;
                    Rec.FixPackageQuantitiesPressed := true;
                    Rec.Modify(true);
                    InventoryAdjustment.PostInventoryAdjustmentLines(Rec);
                end;
            }
            action(TransferLines)
            {
                Caption = 'Transfer Lines';
                Image = AdjustEntries;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Enabled = Rec.FixPackageQuantitiesPressed and not Rec.TransferLinesPressed;
                ToolTip = 'Executes the Transfer Lines action.';

                trigger OnAction()
                var
                    InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
                    PackageTransferHeader: Record "Package Transfer Header IPK";
                begin
                    InventoryAdjustmentLine.SetLoadFields("Package No.");
                    InventoryAdjustmentLine.SetRange("Document No.", Rec."No.");
                    InventoryAdjustmentLine.SetFilter("Physical Quantity", '>0');
                    InventoryAdjustmentLine.FindSet(false);
                    PackageTransferHeader.Init();

                    PackageTransferHeader.Insert(true);
                    PackageTransferHeader.Validate("Transfer-to Code", 'SAYIMDEPO');
                    PackageTransferHeader.Validate("Package Transfer Type", PackageTransferHeader."Package Transfer Type"::"Inventory Adjustment");
                    repeat
                        PackageTransferHeader.Validate(Barcode, InventoryAdjustmentLine."Package No.");
                    until InventoryAdjustmentLine.Next() = 0;

                    PackageTransferHeader.Modify(true);
                    Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);

                    Message('Transfer created');
                    Rec.TransferLinesPressed := true;
                    Rec."Transfer Order No." := PackageTransferHeader."No.";
                    Rec.Modify(true);
                end;
            }
            action(ClearLocation)
            {
                ApplicationArea = All;
                Caption = 'Clear Location';
                Enabled = Rec.FixPackageQuantitiesPressed and Rec.TransferLinesPressed and not Rec.ClearLocationPressed;
                Image = AdjustEntries;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Clear Location action.';

                trigger OnAction()
                var
                    ItemJournalLine: Record "Item Journal Line";
                    ItemJournal: Page "Item Journal";
                begin
                    InventoryAdjustment.ClearRemainingInventoryAtLocation(Rec);
                    ItemJournalLine.SetRange("Journal Batch Name", Rec."Journal Batch Name");
                    ItemJournalLine.SetRange("Journal Template Name", Rec."Journal Template Name");
                    Message('Line Details Created!');
                    Rec.ClearLocationPressed := true;
                    Rec.Modify(true);
                end;
            }
            action(ShowItemLines)
            {
                ApplicationArea = All;
                Caption = 'Show Item Lines';
                Image = List;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = false;
                ToolTip = 'Show Inventory Adjustment Item Lines for this document.';
                trigger OnAction()
                var
                    InventoryAdjustmentItemList: Record "Inventory Adjustment Item List";
                begin
                    InventoryAdjustmentItemList.SetRange("Document No.", Rec."No.");
                    PAGE.Run(PAGE::"Inventory Adjustment Item List", InventoryAdjustmentItemList);
                end;
            }

        }
    }


    var
        InventoryAdjustment: Codeunit "Inventory Adjustment IPK";


}