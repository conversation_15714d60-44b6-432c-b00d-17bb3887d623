pageextension 60003 "Whse. Receipt Subform MXW" extends "Whse. Receipt Subform"
{
    layout
    {
        addafter(Description)
        {
            field("Package Count MXW"; Rec."Package Count MXW")
            {
                ApplicationArea = All;
            }
            field("Total Package Quantity MXW"; Rec."Total Package Quantity MXW")
            {
                ApplicationArea = All;
            }
            field("Lot No. MXW"; Rec."Lot No. MXW")
            {
                ApplicationArea = All;
            }
            field("Item Tracking Info Assignd MXW"; Rec."Item Tracking Info Assignd MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies whether item tracking information is assigned.';
            }
            field("Quality Control Documents MXW"; Rec."Quality Control Documents MXW")
            {
                ApplicationArea = All;
            }
            field("Quality Control Doc. No. MXW"; Rec."Quality Control Doc. No. MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the quality control document number.';
            }
            field("Quality Control Doc. Stat. MXW"; Rec."Quality Control Doc. Stat. MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the quality control document status.';
            }
        }
    }

    actions
    {
        addfirst("&Line")
        {
            action("CreatePackage MXW")
            {
                ApplicationArea = All;
                Caption = 'Create Package';
                Image = Action;
                ToolTip = 'Creates a package from the warehouse receipt line.';

                trigger OnAction()
                begin
                    MaxwellPurchaseManagement.CreatePackageFromWarehouseReceiptLine(Rec, Enum::"Package Creation Method MXW"::Single);
                end;
            }
            action("Assign Item Tracking Info. MXW")
            {
                ApplicationArea = All;
                Caption = 'Assign Item Tracking Info.';
                Image = Action;
                ToolTip = 'Assigns item tracking information to the warehouse receipt line.';

                trigger OnAction()
                begin
                    MaxwellPurchaseManagement.AssignItemTrackingInformationFromWarehouseReceiptLine(Rec);
                end;
            }
            action("Package Details MXW")
            {
                ApplicationArea = All;
                Caption = 'Package Details';
                Image = View;
                ToolTip = 'Shows the package details for this warehouse receipt line.';

                trigger OnAction()
                var
                    WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW";
                begin
                    WarehouseReceiptLineDtl.SetRange("Document No.", Rec."No.");
                    WarehouseReceiptLineDtl.SetRange("Document Line No.", Rec."Line No.");
                    Page.Run(Page::"Whse. Receipt Line Details MXW", WarehouseReceiptLineDtl);
                end;
            }
            action("Generate Quality Control Line MXW")
            {
                ApplicationArea = All;
                Caption = 'Generate Quality Control Line';
                Image = Task;
                ToolTip = 'Generates a quality control document for this warehouse receipt line.';

                trigger OnAction()
                begin
                    MaxwellPurchaseManagement.GenerateQualityControlLineFromWarehouseReceiptLine(Rec);
                end;
            }
        }
    }

    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
}
