page 60055 "Package Transfer Details IPK"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Details';
    PageType = List;
    SourceTable = "Package Transfer Line IPK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field("Document No."; Rec."Document No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                }
                field("Quantity To Transfer"; Rec."Quantity To Transfer")
                {
                }
                field(Received; Rec.Received)
                {
                }
                field("Package Creation Date"; Rec."Package Creation Date")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }

            }
        }
    }
}