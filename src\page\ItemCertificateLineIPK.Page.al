page 60009 "Item Certificate Line IPK"
{
    ApplicationArea = All;
    Caption = 'Item Certificate Line';
    PageType = List;
    SourceTable = "Item Certificate Line IPK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Certificate No."; Rec."Certificate No.")
                {
                    ToolTip = 'Specifies the value of the Certificate No. field.';
                }
                field("Certificate Authority"; Rec."Certificate Authority")
                {
                }
                field("Item Type"; Rec."Item Type")
                {
                }
                field("Certificate ID"; Rec."Certificate ID")
                {
                }
            }
        }
    }
}