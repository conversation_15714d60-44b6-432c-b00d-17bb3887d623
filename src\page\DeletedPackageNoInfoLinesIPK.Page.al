page 60051 "DeletedPackageNoInfoLines IPK"
{
    ApplicationArea = All;
    Caption = 'DeletedPackageNoInfoLines';
    PageType = List;
    SourceTable = "DeletedPackageNoInfoLines IPK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    ToolTip = 'Specifies the value of the Unit of Measure Code field.';
                }
                field("Lot No."; Rec."Lot No.")
                {
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Line Item No."; Rec."Line Item No.")
                {
                    ToolTip = 'Specifies the value of the Line Item No. field.';
                }
                field("Line Variant Code"; Rec."Line Variant Code")
                {
                    ToolTip = 'Specifies the value of the Line Variant Code field.';
                }
                field("Total Qty. on Location"; Rec."Total Qty. on Location")
                {
                }
                field("Production Location"; Rec."Production Location")
                {
                }
                field("Source Package No. IPK"; Rec."Source Package No. IPK")
                {
                }
                field("Last Adjustment"; Rec."Last Adjustment")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
            }
        }

    }
    actions
    {
        area(Creation)
        {
            action(RestroreDeletedPackages)
            {
                Caption = 'Restrore Deleted Packages', Comment = 'TRK="Restrore Deleted Packages"';
                Image = Restore;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Restrore Deleted Packages action.';
                Visible = true;
                trigger OnAction()

                begin
                    SetSelectionFilter(Rec);
                    Rec.FindSet();

                    CreatePackageFromDeletedPackageLines(Rec);
                end;
            }
        }
    }
    procedure CreatePackageFromDeletedPackageLines(var DeletedPackageNoInfoLines: Record "DeletedPackageNoInfoLines IPK")
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageNoInformation: Record "Package No. Information";
    begin
        repeat
            PackageNoInfoLine.SetRange("Package No.", DeletedPackageNoInfoLines."Package No.");
            if not PackageNoInfoLine.FindFirst() then begin
                PackageNoInformation.Init();
                PackageNoInformation."Package No." := DeletedPackageNoInfoLines."Package No.";
                PackageNoInformation."Location Code IPK" := DeletedPackageNoInfoLines."Location Code";
                PackageNoInformation.Insert(true);
                PackageNoInfoLine.Init();
                PackageNoInfoLine."Package No." := PackageNoInformation."Package No.";
                Evaluate(PackageNoInfoLine."Line No.", DeletedPackageNoInfoLines."Line Item No.");
                PackageNoInfoLine."Line Item No." := DeletedPackageNoInfoLines."Line Item No.";
                PackageNoInfoLine."Line Variant Code" := DeletedPackageNoInfoLines."Line Variant Code";
                PackageNoInfoLine.Quantity := DeletedPackageNoInfoLines.Quantity;
                PackageNoInfoLine."Lot No." := DeletedPackageNoInfoLines."Lot No.";
                PackageNoInfoLine.Insert(true);
            end;
            DeletedPackageNoInfoLines.Description := 'Restored';
        until DeletedPackageNoInfoLines.Next() = 0;

    end;
}