report 60011 "Inventory Adjustment Lines IPK"
{
    ApplicationArea = All;
    Caption = 'Inventory Adjustment Lines';
    UsageCategory = ReportsAndAnalysis;
    RDLCLayout = 'src/report/InventorybyLocation.rdlc';
    dataset
    {
        dataitem(InventoryAdjustmentLineIPK; "Inventory Adjustment Line IPK")
        {
            column(DocumentNo; "Document No.")
            {
            }
            column(LineNo; "Line No.")
            {
            }
            column(PackageNo; "Package No.")
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(LineItemNo; "Line Item No.")
            {
            }
            column(LineVariantCode; "Line Variant Code")
            {
            }
            column(TotalQuantity; "Total Quantity")
            {
            }
            column(PhysicalQuantity; "Physical Quantity")
            {
            }
            column(QuantityDifference; "Quantity Difference")
            {
            }
            column(CurrentLocationCode; "Current Location Code")
            {
            }
            column(AdjustmentType; "Adjustment Type")
            {
            }
            column(LineItemUomCode; "Line Item Uom. Code")
            {
            }
            column(LineItemDescription; "Line Item Description")
            {
            }
            column(NoSeries; "No. Series")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
        }
    }
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             group(GroupName)
    //             {
    //             }
    //         }
    //     }
    //     actions
    //     {
    //         area(Processing)
    //         {
    //         }
    //     }
    // }
}