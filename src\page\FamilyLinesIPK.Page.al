page 60066 "Family Lines IPK"
{
    ApplicationArea = All;
    Caption = 'Family Lines';
    PageType = List;
    SourceTable = "Family Line";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Family No."; Rec."Family No.")
                {
                    ToolTip = 'Specifies the value of the Family No. field.';
                }
                field("Line No."; Rec."Line No.")
                {
                    ToolTip = 'Specifies the line number of the product family line.';
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies which items belong to a family.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies a description for the product family line.';
                }
                field("Description 2"; Rec."Description 2")
                {
                    ToolTip = 'Specifies an extended description if there is not enough space in the Description field.';
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    ToolTip = 'Specifies how each unit of the item or resource is measured, such as in pieces or hours. By default, the value in the Base Unit of Measure field on the item or resource card is inserted.';
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the quantity for the item in this family line.';
                }
                field("Variant Code IPK"; Rec."Variant Code IPK")
                {
                }
            }
        }
    }
}