pageextension 60013 "Warehouse Receipt IPK" extends "Warehouse Receipt"
{
    layout
    {
        addafter("No.")
        {
            field("Vendor No. IPK"; Rec."Vendor No. IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Vendor No. field.';
            }
            field("Vendor Name IPK"; Rec."Vendor Name IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Vendor Name field.';
            }
            field("Quality Control Not Processed IPK"; Rec."Quality Control Not Proc. IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Quality Control Not Processed field.';
            }
        }
        // modify("Vendor Shipment No.")
        // {
        //     trigger OnAfterValidate()
        //     begin
        //         IpekPurchaseManagement.WareHouseRecipt_VendorShipmentNo_OnAfterValidate(Rec);
        //     end;
        // }
    }
    actions
    {
        addfirst("F&unctions")
        {
            action("AssignItemTrackingInfo IPK")
            {
                ApplicationArea = All;
                Caption = 'Assign Item Tracking Info.';
                Image = LotInfo;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Assign Item Tracking Info. action.';

                trigger OnAction()
                begin
                    IpekPurchaseManagement.AssignItemTrackingInformationFromWarehouseReceiptHeader(Rec);
                end;
            }

            // action("PostTransferDocuments IPK")
            // {
            //     ApplicationArea = All;
            //     Caption = 'Post Transfer Documents.';
            //     Image = Post;
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     ToolTip = 'Executes the Assign Post Transfer Documents. action.';
            //     trigger OnAction()
            //     begin
            //         IpekQualityManagement.PostAutoTransferOrders(Rec);
            //     end;

            // }

        }
    }
    var
        // IpekQualityManagement: Codeunit "Ipek Quality Management IPK";
        IpekPurchaseManagement: Codeunit "Ipek Purchase Management IPK";


}