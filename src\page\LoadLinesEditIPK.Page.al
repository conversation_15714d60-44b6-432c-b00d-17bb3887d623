page 60037 "Load Lines - Edit IPK"
{
    ApplicationArea = All;
    Caption = 'Load Lines - Edit';
    PageType = List;
    SourceTable = "Load Line IPK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Warehouse Shipment No."; Rec."Warehouse Shipment No.")
                {
                    ToolTip = 'Specifies the value of the Warehouse Shipment No. field.';
                }
                field("Document No."; Rec."Document No.")
                {
                    ToolTip = 'Specifies the value of the Document No. field.';
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Label No."; Rec."Label No.")
                {
                    ToolTip = 'Specifies the value of the Label No. field.';
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Warehouse Shipment Line No."; Rec."Warehouse Shipment Line No.")
                {
                    ToolTip = 'Specifies the value of the Warehouse Shipment Line No. field.';
                }
                field("Source No."; Rec."Source No.")
                {
                    ToolTip = 'Specifies the value of the Source No. field.';
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("Source Line No."; Rec."Source Line No.")
                {
                }
                field("Load Planning Line No."; Rec."Load Planning Line No.")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Total Read Quantity"; Rec."Total Read Quantity")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
            }
        }
    }
}