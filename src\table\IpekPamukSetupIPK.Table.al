table 60004 "Ipek Pamuk Setup IPK"
{
    Caption = 'Ipek Pamuk Setup';

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }

        field(2; "Load No. Series"; Code[20])
        {
            Caption = 'Load No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Load No. Series field.';
        }
        field(3; "Certificate No. Series"; Code[20])
        {
            Caption = 'Certificate No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Certificate No. Series field.';
        }
        field(4; "Freight Item Charge No."; Code[20])
        {
            Caption = 'Freight Item Charge No.';
            ToolTip = 'Specifies the value of the Freight Item Charge No. field.';
            TableRelation = "Item Charge"."No.";
        }
        field(5; "Shift Start Time"; Time)
        {
            Caption = 'Shift Start Time';
            ToolTip = 'Specifies the value of the Shift Start Time field.';
        }
        field(6; "Consumption Jnl. Template Name"; Code[10])
        {
            Caption = 'Consumption Journal Template Name';
            TableRelation = "Item Journal Template" where(Type = const(Consumption));
            ToolTip = 'Specifies the value of the Consumption Journal Template Name field.';
        }
        field(7; "Consumption Jnl. Batch Name"; Code[10])
        {
            Caption = 'Consumption Journal Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Consumption Jnl. Template Name"));
            ToolTip = 'Specifies the value of the Consumption Journal Batch Name field.';
        }
        field(8; "Output Journal Template Name"; Code[10])
        {
            Caption = 'Output Journal Template Name';
            TableRelation = "Item Journal Template" where(Type = const(Output));
            ToolTip = 'Specifies the value of the Output Journal Template Name field.';
        }
        field(9; "Output Journal Batch Name"; Code[10])
        {
            Caption = 'Output Journal Template Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Output Journal Template Name"));
            ToolTip = 'Specifies the value of the Output Journal Template Name field.';
        }
        field(10; "Package Transfer No. Series"; Code[20])
        {
            Caption = 'Package Transfer No. Series';
            ToolTip = 'Specifies the value of the Package Transfer No. Series field.';
            TableRelation = "No. Series".Code;

        }
        field(11; "Package Tran. Jnl. Temp. Name"; Code[10])
        {
            Caption = 'Package Transfer Journal Template Name';
            ToolTip = 'Specifies the value of the Package Transfer Journal Template Name field.';
            TableRelation = "Item Journal Template".Name where(Type = const(Transfer));
        }
        field(12; "Package Tran. Jnl. Batch Name"; Code[10])
        {
            Caption = 'Package Transfer Journal Batch Name';
            ToolTip = 'Specifies the value of the Package Transfer Journal Batch Name field.';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Tran. Jnl. Temp. Name"));
        }
        field(13; "Package Combine No. Series"; Code[20])
        {
            Caption = 'Package Combine No. Series';
            ToolTip = 'Specifies the value of the Package Combine No. Series field.';
            TableRelation = "No. Series".Code;

        }
        field(14; "Load Card Mail Group"; Text[200])
        {
            Caption = 'Load Card Mail Group';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Load Card Mail Group field.';
        }
        field(15; "Warehouse Recipt Mail Group"; Text[200])
        {
            Caption = 'Warehouse Recipt Mail Group';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Warehouse Recipt Mail Group field.';
        }
        field(16; "Quality Control Mail Group"; Text[200])
        {
            Caption = 'Quality Control Mail Group';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Quality Control Mail Group field.';
        }
        field(17; "Warehouse Recipt Created M.G."; Text[200])
        {
            Caption = 'Warehouse Recipt Created Mail Group';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Warehouse Recipt Created Mail Group field.';
        }
        field(18; "Def. Customs Officer IPK"; Text[50])
        {
            Caption = 'Default Customs Officer';
            TableRelation = "Customs Officer IPK".Code;
            ToolTip = 'Specifies the value of the Default Customs Officer field.';
        }
        field(19; "Def. Load Location"; Text[50])
        {
            Caption = 'Default Load Location';
            ToolTip = 'Specifies the value of the Default Load Location field.';
        }
        field(20; "BOM Change Mail Group"; Text[200])
        {
            Caption = 'BOM Change Mail Group';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the BOM Change Mail Group field.';
        }
        field(21; "Item Ledger Mail Group"; Text[200])
        {
            Caption = 'Item Ledger Mail Group';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Item Ledger Mail Group field.';
        }
        field(22; "Cutting Item No."; Code[20])
        {
            Caption = 'Cutting Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Cutting Item No. field.';
        }
        field(23; "Waterjet Item Category Filter"; Text[50])
        {
            Caption = 'Waterjet Item Category Filter';
            ToolTip = 'Specifies the filter value for waterjet item categories (e.g., YM.01*).';
        }
        field(24; "Organic Cutting Item No."; Code[20])
        {
            Caption = 'Organic Cutting Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Organic Cutting Item No. field.';
        }
        field(25; "Item Price Dataset Start Date"; Date)
        {
            Caption = 'Item Price Dataset Start Date';
            ToolTip = 'Specifies the start date for processing Item Ledger Entries in the price dataset calculation.';
        }
        field(26; "Default Cutting Location"; Code[10])
        {
            Caption = 'Default Cutting Location';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the Default Cutting Location in the price dataset calculation.';
        }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    var
        RecordHasBeenRead: Boolean;

    procedure GetRecordOnce()
    begin
        if RecordHasBeenRead then
            exit;
        Get();
        RecordHasBeenRead := true;
    end;

    procedure InsertIfNotExists()
    begin
        Reset();
        if not Get() then begin
            Init();
            Insert(true);
        end;
    end;

    trigger OnInsert()
    begin
        if "Item Price Dataset Start Date" = 0D then
            "Item Price Dataset Start Date" := DMY2Date(1, 1, 2025); // Default to 01.01.2025
    end;


}