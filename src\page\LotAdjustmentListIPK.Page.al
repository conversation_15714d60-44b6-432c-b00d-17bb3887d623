page 60052 "Lot Adjustment List IPK"
{
    ApplicationArea = All;
    Caption = 'Adjustment List';
    PageType = List;
    SourceTable = "Lot Adjustment Line IPK";
    UsageCategory = Lists;
    // SourceTableTemporary = true;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Line Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the value of the Item No. field.';
                }
                field("Line Variant Code"; Rec."Variant Code")
                {
                    ToolTip = 'Specifies the value of the Variant Code field.';
                }
                field("Total Qty. on Location"; Rec."Total Qty. on Location")
                {
                }
                field("Total Qty. on Loc. Pack"; Rec."Physical Quantity")
                {
                }
            }
        }
    }
}