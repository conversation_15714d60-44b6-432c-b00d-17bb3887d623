query 60003 "Package No. Info. Line IPK"
{
    Caption = 'Package No. Info. Line IPK';
    QueryType = Normal;

    elements
    {
        dataitem(PackageNoInfoLineIPK; "Package No. Info. Line IPK")
        {
            column(PackageNo; "Package No.")
            {
            }
            column(LineItemNo; "Line Item No.")
            {
            }
            column(LineVariantCode; "Line Variant Code")
            {
            }
            column(Description; Description)
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(LastAdjustment; "Last Adjustment")
            {
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}