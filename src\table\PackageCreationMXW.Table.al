table 60001 "Package Creation MXW"
{
    Caption = 'Package Creation';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the document number.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the line number within the document.';
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the item number.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get(Rec."Item No.") then
                    Rec."Item Description" := Item.Description;
            end;
        }
        field(4; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies a description of the item.';
        }
        field(5; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number.';
        }
        field(6; "Package Count"; Integer)
        {
            Caption = 'Package Count';
            ToolTip = 'Specifies the package count.';
        }
        field(7; "Package Quantity"; Decimal)
        {
            Caption = 'Package Quantity';
            ToolTip = 'Specifies the package quantity.';
        }
        field(8; "Creation Method"; Enum "Package Creation Method MXW")
        {
            Caption = 'Creation Method';
            ToolTip = 'Specifies the package creation method.';
            trigger OnValidate()
            begin
                case Rec."Creation Method" of
                    Rec."Creation Method"::Single:
                        begin
                            Rec.Validate("Package Count", 1);
                            Rec.Validate("Package Quantity", 0);
                        end;
                    Rec."Creation Method"::Multiple:
                        begin
                            Rec.Validate("Package Count", 0);
                            Rec.Validate("Package Quantity", 0);
                        end;
                end;
            end;
        }
        field(9; "Source Type"; Enum "Package Creation Src. Type MXW")
        {
            Caption = 'Source Type';
            ToolTip = 'Specifies the source type of the package creation.';
            Editable = false;
        }
        field(10; "Item Pieces Of Pallet"; Decimal)
        {
            Caption = 'Item Pieces Of Pallet';
            ToolTip = 'Specifies the number of pieces per pallet.';
            AllowInCustomizations = Always;
        }
        field(11; "Order Quantity"; Decimal)
        {
            Caption = 'Order Quantity';
            ToolTip = 'Specifies the order quantity.';
        }
        field(12; "Remaining Quantity"; Decimal)
        {
            Caption = 'Remaining Quantity';
            ToolTip = 'Specifies the remaining quantity.';
            AllowInCustomizations = Always;
        }
        field(13; "Max Available Quantity"; Decimal)
        {
            Caption = 'Max Available Quantity';
            ToolTip = 'Specifies the maximum available quantity.';
        }
        field(14; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the variant code.';
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if ("Variant Code" <> '') and ("Item No." <> '') then
                    if ItemVariant.Get("Item No.", "Variant Code") then
                        Rec."Item Description" := ItemVariant.Description;
            end;
        }

    }

    keys
    {
        key(PK; "Document No.", "Document Line No.")
        {
            Clustered = true;
        }
    }
}
