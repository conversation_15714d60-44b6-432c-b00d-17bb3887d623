query 60005 "G/L Entry IPK"
{
    Caption = 'G/L Entry';
    QueryType = Normal;

    elements
    {
        dataitem(GLEntry; "G/L Entry")
        {
            column(EntryNo; "Entry No.")
            {
            }
            column(GLAccountNo; "G/L Account No.")
            {
            }
            column(PostingDate; "Posting Date")
            {
            }
            column(DocumentType; "Document Type")
            {
            }
            column(DocumentNo; "Document No.")
            {
            }
            column(Description; Description)
            {
            }
            column(BalAccountNo; "Bal. Account No.")
            {
            }
            column(Amount; Amount)
            {
            }
            column(SourceCurrencyAmount; "Source Currency Amount")
            {
            }
            column(SourceCurrencyVATAmount; "Source Currency VAT Amount")
            {
            }
            column(SourceCurrencyCode; "Source Currency Code")
            {
            }
            column(GlobalDimension1Code; "Global Dimension 1 Code")
            {
            }
            column(GlobalDimension2Code; "Global Dimension 2 Code")
            {
            }
            column(UserID; "User ID")
            {
            }
            column(SourceCode; "Source Code")
            {
            }
            column(SystemCreatedEntry; "System-Created Entry")
            {
            }
            column(PriorYearEntry; "Prior-Year Entry")
            {
            }
            column(JobNo; "Job No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(VATAmount; "VAT Amount")
            {
            }
            column(BusinessUnitCode; "Business Unit Code")
            {
            }
            column(JournalBatchName; "Journal Batch Name")
            {
            }
            column(ReasonCode; "Reason Code")
            {
            }
            column(GenPostingType; "Gen. Posting Type")
            {
            }
            column(GenBusPostingGroup; "Gen. Bus. Posting Group")
            {
            }
            column(GenProdPostingGroup; "Gen. Prod. Posting Group")
            {
            }
            column(BalAccountType; "Bal. Account Type")
            {
            }
            column(TransactionNo; "Transaction No.")
            {
            }
            column(DebitAmount; "Debit Amount")
            {
            }
            column(CreditAmount; "Credit Amount")
            {
            }
            column(DocumentDate; "Document Date")
            {
            }
            column(ExternalDocumentNo; "External Document No.")
            {
            }
            column(SourceType; "Source Type")
            {
            }
            column(SourceNo; "Source No.")
            {
            }
            column(NoSeries; "No. Series")
            {
            }
            column(TaxAreaCode; "Tax Area Code")
            {
            }
            column(TaxLiable; "Tax Liable")
            {
            }
            column(TaxGroupCode; "Tax Group Code")
            {
            }
            column(UseTax; "Use Tax")
            {
            }
            column(VATBusPostingGroup; "VAT Bus. Posting Group")
            {
            }
            column(VATProdPostingGroup; "VAT Prod. Posting Group")
            {
            }
            column(AdditionalCurrencyAmount; "Additional-Currency Amount")
            {
            }
            column(AddCurrencyDebitAmount; "Add.-Currency Debit Amount")
            {
            }
            column(AddCurrencyCreditAmount; "Add.-Currency Credit Amount")
            {
            }
            column(CloseIncomeStatementDimID; "Close Income Statement Dim. ID")
            {
            }
            column(ICPartnerCode; "IC Partner Code")
            {
            }
            column(Reversed; Reversed)
            {
            }
            column(ReversedbyEntryNo; "Reversed by Entry No.")
            {
            }
            column(ReversedEntryNo; "Reversed Entry No.")
            {
            }
            column(GLAccountName; "G/L Account Name")
            {
            }
            column(JournalTemplName; "Journal Templ. Name")
            {
            }
            column(VATReportingDate; "VAT Reporting Date")
            {
            }
            column(DimensionSetID; "Dimension Set ID")
            {
            }
            column(ShortcutDimension3Code; "Shortcut Dimension 3 Code")
            {
            }
            column(ShortcutDimension4Code; "Shortcut Dimension 4 Code")
            {
            }
            column(ShortcutDimension5Code; "Shortcut Dimension 5 Code")
            {
            }
            column(ShortcutDimension6Code; "Shortcut Dimension 6 Code")
            {
            }
            column(ShortcutDimension7Code; "Shortcut Dimension 7 Code")
            {
            }
            column(ShortcutDimension8Code; "Shortcut Dimension 8 Code")
            {
            }
            column(LastDimCorrectionEntryNo; "Last Dim. Correction Entry No.")
            {
            }
            column(LastDimCorrectionNode; "Last Dim. Correction Node")
            {
            }
            column(DimensionChangesCount; "Dimension Changes Count")
            {
            }
            column(AllocationAccountNo; "Allocation Account No.")
            {
            }
            column(ProdOrderNo; "Prod. Order No.")
            {
            }
            column(FAEntryType; "FA Entry Type")
            {
            }
            column(FAEntryNo; "FA Entry No.")
            {
            }
            column(Comment; Comment)
            {
            }
            column(NonDeductibleVATAmount; "Non-Deductible VAT Amount")
            {
            }
            column(NonDeductibleVATAmountACY; "Non-Deductible VAT Amount ACY")
            {
            }
            column(AccountId; "Account Id")
            {
            }
            column(LastModifiedDateTime; "Last Modified DateTime")
            {
            }
            column(VATWthhldBusPostingGrpINF; "VAT Wthhld Bus Posting Grp INF")
            {
            }
            column(VATWthldProdPostingGrpINF; "VAT Wthld Prod Posting Grp INF")
            {
            }
            column(VATWithheldAmountINF; "VAT Withheld Amount INF")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}