pageextension 60035 "Prod. Order Components IPK" extends "Prod. Order Components"
{
    layout
    {
        modify("Variant Code")
        {
            Visible = true;
        }
        modify("Location Code")
        {
            Visible = true;
        }
        addafter("Remaining Quantity")
        {
            field("Consumed Quantity IPK"; Rec."Consumed Quantity IPK")
            {
                ApplicationArea = All;
            }
            field("Quantity At Location IPK"; Rec."Quantity At Location IPK")
            {
                ApplicationArea = All;
            }
            field("Quantity At All Locations IPK"; Rec."Quantity At All Locations IPK")
            {
                ApplicationArea = All;
            }
        }
        modify("Scrap %")
        {
            Visible = true;
            Editable = false;
        }
        moveafter("Consumed Quantity IPK"; "Scrap %")
        addafter(Description)
        {
            field("Category Code IPK"; Rec."Category Code IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Category Code field.';
            }
            field("Category Description IPK"; Rec."Category Description IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Category Description field.';
            }
        }
    }
}