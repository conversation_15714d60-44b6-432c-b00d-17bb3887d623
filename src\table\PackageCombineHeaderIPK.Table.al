table 60022 "Package Combine Header IPK"
{
    Caption = 'Package Combine Header';
    DataClassification = CustomerContent;
    DrillDownPageId = "Package Combine List IPK";
    LookupPageId = "Package Combine List IPK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the value of the No. field.';
        }
        field(2; "Item No. Filter"; Code[20])
        {
            Caption = 'Item No. Filter';
            ToolTip = 'Specifies the value of the Item No. Filter field.';
            TableRelation = Item."No.";

        }
        field(3; "Variant Code Filter"; Code[10])
        {
            Caption = 'Variant Code Filter';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No. Filter"));
            ToolTip = 'Specifies the value of the Variant Code Filter field.';
        }
        field(4; "Location Code Filter"; Code[10])
        {
            Caption = 'Location Code Filter';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Location Code Filter field.';
            TableRelation = Location.Code where("Production Location IPK" = const(true));
        }
        field(5; "Operation Date-Time"; DateTime)
        {
            Editable = false;
            Caption = 'Operation Date-Time';
            ToolTip = 'Specifies the value of the Operation Date-Time field.';
        }
        field(6; "New Package Quantity"; Decimal)
        {
            Caption = 'New Package Quantity';
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the value of the New Package Quantity field.';
        }
        field(7; "Total Quantity-to Add"; Decimal)
        {
            Caption = 'Total Quantity-to Add';
            FieldClass = FlowField;
            Editable = false;
            DecimalPlaces = 0 : 5;
            CalcFormula = sum("Package Combine Line IPK"."Quantity-to Add" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Quantity-to Add field.';
        }
        field(8; "New Package No."; Code[50])
        {
            Editable = false;
            Caption = 'New Package No.';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the New Package No. field.';
        }
        field(9; Received; Boolean)
        {
            Caption = 'Received';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Received field.';
        }
        field(10; "Target Package No."; Code[50])
        {
            Caption = 'Target Package No.';
            DataClassification = ToBeClassified;
            TableRelation = "Package No. Information"."Package No." where("Location Code IPK" = field("Target Location"), "Line Item No. IPK" = field("Item No. Filter"), "Line Variant Code IPK" = field("Variant Code Filter"));
            ToolTip = 'Specifies the value of the Target Package No. field.';
        }
        field(11; "Target Location"; Code[10])
        {
            Caption = 'Target Location';
            DataClassification = ToBeClassified;
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Target Location field.';
        }
        field(12; Shipped; Boolean)
        {
            Caption = 'Shipped';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Shipped field.';
        }
        field(13; Combined; Boolean)
        {
            Caption = 'Combined';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Combined field.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
    begin
        Clear(NoSeries);

        if Rec."No." = '' then begin
            IpekPamukSetup.GetRecordOnce();
            IpekPamukSetup.TestField("Package Combine No. Series");
            Rec."No. Series" := IpekPamukSetup."Package Combine No. Series";
            if NoSeries.AreRelated(IpekPamukSetup."Package Combine No. Series", xRec."No. Series") then
                Rec."No. Series" := xRec."No. Series";

            Rec."No." := NoSeries.GetNextNo(IpekPamukSetup."Package Combine No. Series");
        end;
        Rec."Operation Date-Time" := CurrentDateTime();

    end;

    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        NoSeries: Codeunit "No. Series";
}