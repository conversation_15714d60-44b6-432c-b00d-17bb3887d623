report 60003 "Packing List IPK"
{
    ApplicationArea = All;
    Caption = 'Packing List';
    UsageCategory = ReportsAndAnalysis;
    //Invoice No sor
    //satır alanlarındaki hesaplamaları sor
    dataset
    {
        dataitem(SalesHeader; "Sales Header")
        {
            column(External_Document_No_; "External Document No.")
            {
            }
            column(Your_Reference; "Your Reference")
            {
            }
            column(Export_No__IPK; "Export No. IPK")
            {
            }
            column(SelltoCustomerNo; "Sell-to Customer No.")
            {
            }
            column(SelltoCustomername; "Sell-to Customer Name")
            {
            }
            column(SelltoAddress; "Sell-to Address")
            {
            }
            column(SelltoAddress2; "Sell-to Address 2")
            {
            }
            column(SelltoCity; "Sell-to City")
            {
            }
            column(SelltoCounty; "Sell-to County")
            {
            }

            dataitem("Sales Line"; "Sales Line")
            {
                DataItemLink = "Document No." = field("No."), "Document Type" = field("Document Type");
                column(No_; "No.")
                {
                }
                column(Description; Description)
                {
                }
                column(Units_per_Parcel; "Units per Parcel")//Number of Packs in a Carton
                {
                }
                column(Net_Weight; "Net Weight")
                {
                }
                column(Gross_Weight; "Gross Weight")
                {
                }
                column(Unit_Volume; "Unit Volume")
                {
                }
                column(Quantity; Quantity)
                {
                }
                column(Line_Net_Weight_IPK; "Line Net Weight IPK")
                {
                }
                column(Line_Gross_Weight_IPK; "Line Gross Weight IPK")
                {
                }
                column(Line_Volume_IPK; "Line Volume IPK")
                {
                }
                column(Line_Unit_Quantity_IPK; "Line Unit Quantity IPK")
                {
                }

            }




        }

    }
    requestpage
    {
        layout
        {

        }
    }
}