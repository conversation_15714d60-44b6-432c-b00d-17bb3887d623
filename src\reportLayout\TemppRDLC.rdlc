﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="SelltoCustomerNo">
          <DataField>SelltoCustomerNo</DataField>
        </Field>
        <Field Name="BilltoAddress">
          <DataField>BilltoAddress</DataField>
        </Field>
        <Field Name="BilltoAddress2">
          <DataField>BilltoAddress2</DataField>
        </Field>
        <Field Name="BilltoCity">
          <DataField>BilltoCity</DataField>
        </Field>
        <Field Name="YourReference">
          <DataField>YourReference</DataField>
        </Field>
        <Field Name="TransportMethod">
          <DataField>TransportMethod</DataField>
        </Field>
        <Field Name="ShipmentDate">
          <DataField>ShipmentDate</DataField>
        </Field>
        <Field Name="PaymentTermsCode">
          <DataField>PaymentTermsCode</DataField>
        </Field>
        <Field Name="NoLbl">
          <DataField>NoLbl</DataField>
        </Field>
        <Field Name="Name">
          <DataField>Name</DataField>
        </Field>
        <Field Name="SWIFT_Code">
          <DataField>SWIFT_Code</DataField>
        </Field>
        <Field Name="Bank_Account_No_">
          <DataField>Bank_Account_No_</DataField>
        </Field>
        <Field Name="IBAN">
          <DataField>IBAN</DataField>
        </Field>
        <Field Name="NoSalesLine">
          <DataField>NoSalesLine</DataField>
        </Field>
        <Field Name="DescriptionSalesLine">
          <DataField>DescriptionSalesLine</DataField>
        </Field>
        <Field Name="QuantitySalesLine">
          <DataField>QuantitySalesLine</DataField>
        </Field>
        <Field Name="QuantitySalesLineFormat">
          <DataField>QuantitySalesLineFormat</DataField>
        </Field>
        <Field Name="UnitPriceSalesLine">
          <DataField>UnitPriceSalesLine</DataField>
        </Field>
        <Field Name="UnitPriceSalesLineFormat">
          <DataField>UnitPriceSalesLineFormat</DataField>
        </Field>
        <Field Name="AmountSalesLine">
          <DataField>AmountSalesLine</DataField>
        </Field>
        <Field Name="AmountSalesLineFormat">
          <DataField>AmountSalesLineFormat</DataField>
        </Field>
        <Field Name="LineUnitQuantityIPKSalesLine">
          <DataField>LineUnitQuantityIPKSalesLine</DataField>
        </Field>
        <Field Name="YourReferenceLbl">
          <DataField>YourReferenceLbl</DataField>
        </Field>
        <Field Name="TransportMethodLbl">
          <DataField>TransportMethodLbl</DataField>
        </Field>
        <Field Name="ShipmentDateLbl">
          <DataField>ShipmentDateLbl</DataField>
        </Field>
        <Field Name="PaymentTermsCodeLbl">
          <DataField>PaymentTermsCodeLbl</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>