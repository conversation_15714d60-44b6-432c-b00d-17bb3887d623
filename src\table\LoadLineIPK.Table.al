table 60001 "Load Line IPK"
{
    Caption = 'Load Line';
    DataClassification = CustomerContent;
    //Access = Internal;
    Extensible = true;
    DrillDownPageId = "Load Lines IPK";
    LookupPageId = "Load Lines IPK";


    fields
    {
        field(1; "Warehouse Shipment No."; Code[20])
        {
            Caption = 'Warehouse Shipment No.';
            TableRelation = "Load Header IPK"."Warehouse Shipment No.";
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Document No."; Code[20]) //xxxxxx
        {
            Caption = 'Document No.';
            TableRelation = "Load Header IPK"."No.";
            ToolTip = 'Specifies the value of the Contaier No. field.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(4; "Label No."; Code[50])
        {
            Caption = 'Label No.';
            ToolTip = 'Specifies the value of the Package No. field.';
            TableRelation = "Package No. Information"."Package No.";
            trigger OnValidate()
            var
                LoadLine: Record "Load Line IPK";
                LoadPlanningLine: Record "Load Planning Line IPK";
                PackageNoInfoLine: Record "Package No. Info. Line IPK";
                PackageNoInformation: Record "Package No. Information";
                WarehouseShipmentHeader: Record "Warehouse Shipment Header";
                ConfirmManagement: Codeunit "Confirm Management";
                Stop: Boolean;
                PackageNoInfoLineQuantity: Decimal;
                RemainingQuantityOnLoadPlanningLine: Decimal;
                AlreadyShippedErr: Label 'This package is already shipped with Warehouse Shipment No.: %1', Comment = '%1="Package No. Information"."Warehouse Shipment No. IPK"';
                DividePackageQst: Label 'This package exeeds the planning lines quantity, do you want to divide this package?';
                InsufficientQuantityErr: Label 'This package have no available quantity to load';
                NoLoadPlanningLineFoundErr: Label 'There is no Load Planning Line for this item.\ Item No.: %1 - Variant No.: %2', Comment = '%1="Lot No. Information"."Item No."; %2="Lot No. Information"."Variant Code"';
                PackageLocationErr: Label 'You need to transfer the palette to Location Code: %1 first.', Comment = '%1="Warehouse Shipment Header"."Location Code"';
                QuantityErr: Label 'You cannot load more then %1 for Item: %2, Variant: %3 but you are trying to load %4 ', Comment = '%1=; %2="Package No. Info. Line IPK"."Line Item No."; %3="Package No. Info. Line IPK"."Line Variant Code"; %4="Package No. Info. Line IPK".Quantity';
            begin
                if Rec."Label No." = '' then
                    exit;

                LoadPlanningLine.SetAutoCalcFields("Quantity Loaded");
                PackageNoInformation.Get('', '', Rec."Label No.");

                // PackageNoInformation.SetRange("Package No.", Rec."Label No.");
                // PackageNoInformation.FindFirst();

                // if PackageNoInformation."Pallet IPK" then begin
                //     ProcessPalletBarcode(PackageNoInformation);
                //     exit;
                // end;

                if PackageNoInformation."Warehouse Shipment No. IPK" <> '' then
                    Error(AlreadyShippedErr, PackageNoInformation."Warehouse Shipment No. IPK");

                WarehouseShipmentHeader.Get(Rec."Warehouse Shipment No.");
                if WarehouseShipmentHeader."Location Code" <> PackageNoInformation."Location Code IPK" then
                    Error(PackageLocationErr, WarehouseShipmentHeader."Location Code");



                PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
                PackageNoInfoLine.SetRange("Item No.", PackageNoInformation."Item No.");
                PackageNoInfoLine.SetRange("Variant Code", PackageNoInformation."Variant Code");
                PackageNoInfoLine.FindSet();

                // LoadPlanningLine.Reset();
                LoadPlanningLine.SetRange("Warehouse Shipment No.", Rec."Warehouse Shipment No.");
                LoadPlanningLine.SetRange("Document No.", Rec."Document No.");
                LoadPlanningLine.SetFilter("Quantity to Load", '>0');



                repeat
                    LoadPlanningLine.SetRange("Item No.", PackageNoInfoLine."Line Item No.");
                    LoadPlanningLine.SetRange("Variant Code", PackageNoInfoLine."Line Variant Code");
                    if not LoadPlanningLine.FindSet(false) then
                        Error(NoLoadPlanningLineFoundErr, PackageNoInfoLine."Line Item No.", PackageNoInfoLine."Line Variant Code");
                    Stop := false;
                    PackageNoInfoLineQuantity := PackageNoInfoLine.Quantity;
                    LoadLine.SetRange("Label No.", PackageNoInformation."Package No.");
                    LoadLine.SetRange("Lot No.", PackageNoInfoLine."Lot No.");
                    LoadLine.SetFilter(Status, '<>%1', LoadLine.Status::Shipped);
                    if LoadLine.FindSet() then
                        repeat
                            PackageNoInfoLineQuantity -= LoadLine.Quantity;
                        until LoadLine.Next() = 0;

                    if PackageNoInfoLineQuantity <= 0 then
                        Error(InsufficientQuantityErr);
                    repeat
                        RemainingQuantityOnLoadPlanningLine := LoadPlanningLine."Quantity to Load" - LoadPlanningLine."Quantity Loaded";
                        if RemainingQuantityOnLoadPlanningLine > 0 then
                            if RemainingQuantityOnLoadPlanningLine >= PackageNoInfoLineQuantity then begin
                                Rec.Validate(Quantity, PackageNoInfoLineQuantity);
                                Rec.Validate("Item No.", PackageNoInfoLine."Line Item No.");
                                Rec.Validate("Variant Code", PackageNoInfoLine."Line Variant Code");
                                Rec.Validate(Description, PackageNoInfoLine.Description);
                                // Rec.Validate("Location Code", PackageNoInfoLine."Location Code");
                                Rec.Validate("Lot No.", PackageNoInfoLine."Lot No.");
                                Rec.Validate("Warehouse Shipment Line No.", LoadPlanningLine."Warehouse Shipment Line No.");
                                Rec.Validate("Source No.", LoadPlanningLine."Source No.");
                                Rec.Validate("Source Line No.", LoadPlanningLine."Source Line No.");
                                Stop := true;
                            end else
                                if ConfirmManagement.GetResponseOrDefault(DividePackageQst) then begin
                                    Rec.Validate(Quantity, LoadPlanningLine."Quantity to Load" - LoadPlanningLine."Quantity Loaded");
                                    Rec.Validate("Item No.", PackageNoInfoLine."Line Item No.");
                                    Rec.Validate("Variant Code", PackageNoInfoLine."Line Variant Code");
                                    Rec.Validate(Description, PackageNoInfoLine.Description);
                                    // Rec.Validate("Location Code", PackageNoInfoLine."Location Code");
                                    Rec.Validate("Lot No.", PackageNoInfoLine."Lot No.");
                                    Rec.Validate("Warehouse Shipment Line No.", LoadPlanningLine."Warehouse Shipment Line No.");
                                    Rec.Validate("Source No.", LoadPlanningLine."Source No.");
                                    Rec.Validate("Source Line No.", LoadPlanningLine."Source Line No.");
                                    Stop := true;
                                end
                                else
                                    Error(QuantityErr, LoadPlanningLine."Quantity to Load" - LoadPlanningLine."Quantity Loaded", PackageNoInfoLine."Line Item No.", PackageNoInfoLine."Line Variant Code", PackageNoInfoLine.Quantity);
                    until Stop or (LoadPlanningLine.Next() = 0);
                until PackageNoInfoLine.Next() = 0;
            end;
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = "Item"."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(6; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant"."Code" where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(7; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
            trigger OnValidate()
            var
                PackageNoInfoLine: Record "Package No. Info. Line IPK";
                AlreadyReadErr: Label 'This palette is already partially used in other load cards. It has only %1 units available, but you are trying to load %2 units.', Comment = '%1=; %2=Quantity';
            begin
                Rec.CalcFields("Total Read Quantity");
                PackageNoInfoLine.SetRange("Package No.", Rec."Label No.");
                PackageNoInfoLine.FindFirst();
                if (PackageNoInfoLine.Quantity) < (Rec.Quantity + Rec."Total Read Quantity") then
                    Error(AlreadyReadErr, (PackageNoInfoLine.Quantity - Rec."Total Read Quantity"), (Rec.Quantity));
                // Message(Format(Rec.Quantity) + '-' + Format(Rec."Total Read Quantity"));
            end;
        }
        field(9; "Warehouse Shipment Line No."; Integer)
        {
            Caption = 'Warehouse Shipment Line No.';
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(10; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = "Sales Header"."No.";
            ToolTip = 'Specifies the value of the Source Document No. field.';
        }
        field(11; "Your Reference"; Text[35])
        {
            Caption = 'Your Reference';
            ToolTip = 'Specifies the value of the Your Reference field.';
        }
        field(12; "Source Line No."; Integer)
        {
            Caption = 'Source Line No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Source Line No. field.';
        }
        field(13; "Load Planning Line No."; Integer)
        {
            Caption = 'Load Planning Line No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Load Planning Line No. field.';
        }
        field(14; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(15; Status; Enum "Load Status IPK")
        {
            Caption = 'Status';
            Editable = false;
            FieldClass = FlowField;
            ToolTip = 'Specifies the value of the Status field.';
            CalcFormula = lookup("Load Header IPK".Status where("No." = field("Document No.")));
        }
        field(16; "Total Read Quantity"; Decimal)
        {
            Caption = 'Total Read Quantity';
            Editable = false;
            ToolTip = 'Specifies the value of the Total Read Quantity field.';
            FieldClass = FlowField;
            CalcFormula = sum("Load Line IPK".Quantity where("Label No." = field("Label No."), Status = filter(<> Shipped)));
        }
        field(17; "Produced At"; DateTime)
        {
            Caption = 'Produced At';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Created At IPK" where("Package No." = field("Label No.")));
            ToolTip = 'Specifies the value of the Produced At field.';
        }
        field(18; "Remaining Quantity At Loc."; Decimal)
        {//alper
            Caption = 'Remaining Quantity At Location';
            Editable = false;
            ToolTip = 'Specifies the value of the Remaining Quantity At Location field.';
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Item No." = field("Item No."),
                                                                 "Variant Code" = field("Variant Code"),
                                                                 "Location Code" = field("Location Code"),
                                                                 "Lot No." = field("Lot No."),
                                                                 Open = const(true)));
        }
        field(19; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            Editable = false;
            FieldClass = FlowField;
            AllowInCustomizations = Always;
            CalcFormula = lookup("Package No. Information"."Location Code IPK" where("Package No." = field("Label No.")));
            ToolTip = 'Specifies the value of the Location Code field.';
        }
    }
    keys
    {
        key(PK; "Warehouse Shipment No.", "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(key2; "Warehouse Shipment No.", "Document No.", "Label No.", "Item No.", "Variant Code", "Lot No.", "Source No.", "Source Line No.")
        {
            Unique = true;
        }
        key(key3; SystemCreatedAt)
        {
        }
    }


    trigger OnInsert()
    var
        LoadLine: Record "Load Line IPK";
    begin
        LoadLine.SetRange("Warehouse Shipment No.", Rec."Warehouse Shipment No.");
        LoadLine.SetRange("Document No.", Rec."Document No.");
        if LoadLine.FindLast() then
            Rec."Line No." := LoadLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}