page 60023 "Package Transfer Orders MXW"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Orders';
    PageType = List;
    SourceTable = "Package Transfer Header MXW";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "Package Transfer Order MXW";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                }

                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                    ApplicationArea = All;
                }

                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Transfer-to Code field.';
                }

                field("Posting Date"; Rec."Posting Date")
                {
                    ApplicationArea = All;
                }

                field(Shipped; Rec.Shipped)
                {
                    ApplicationArea = All;
                }

                field("Received"; Rec.Received)
                {
                    ApplicationArea = All;
                }

                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                    ApplicationArea = All;
                }

                field("Palette Count"; Rec."Palette Count")
                {
                    ApplicationArea = All;
                }

                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}
