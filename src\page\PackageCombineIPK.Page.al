page 60040 "Package Combine IPK"
{
    ApplicationArea = All;
    Caption = 'Package Combine';
    PageType = Card;
    SourceTable = "Package Combine Header IPK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {

                Caption = 'General';

                Editable = not Rec.Combined;
                field("No."; Rec."No.")
                {
                    Editable = not Rec.Shipped;
                }
                field("Item No. Filter"; Rec."Item No. Filter")
                {
                    Editable = not Rec.Shipped;
                }
                field("Variant Code Filter"; Rec."Variant Code Filter")
                {
                    Editable = not Rec.Shipped;
                }
                field("Location Code Filter"; Rec."Location Code Filter")
                {
                    Editable = not Rec.Shipped;
                }
                field("Target Location"; Rec."Target Location")
                {
                    Editable = (not Rec.Received);
                }
                field("New Package Quantity"; Rec."New Package Quantity")
                {
                    Editable = (not Rec.Shipped);
                }
                field("Total Quantity-to Add"; Rec."Total Quantity-to Add")
                {
                }
                field("Operation Date-Time"; Rec."Operation Date-Time")
                {
                    Editable = (not Rec.Shipped);
                }
                field(Shipped; Rec.Shipped)
                {
                    Editable = not Rec.Received;
                }
                field(Received; Rec.Received)
                {
                    Editable = not Rec.Received;
                }
                field(Combined; Rec.Combined)
                {
                    Editable = not Rec.Combined;
                }
                field("New Package No."; Rec."New Package No.")
                {
                    Enabled = (not Rec.Shipped);
                }
                field("Target Package No."; Rec."Target Package No.")
                {
                    Editable = Rec."Target Location" <> '';
                }


            }
            part(Lines; "Package Combine Subpage IPK")
            {
                Enabled = (not Rec.Shipped);
                Caption = 'Lines';
                UpdatePropagation = Both;
                SubPageLink = "Document No." = field("No.");

            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PopulateTable)
            {
                Caption = 'Populate Table';
                ToolTip = 'Executes the Populate Table action.';
                Image = PostApplication;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Enabled = not Rec.Shipped;
                trigger OnAction()
                // Rec: Record "Package Merge IPK";

                begin
                    IpekPackageTransMgt.PackageCombinePopulateTable(Rec);

                end;
            }
            action(Ship)
            {
                Caption = 'Ship';
                ToolTip = 'Executes the Post action.';
                Enabled = (not Rec.Shipped) and (Rec."Total Quantity-to Add" > 0);
                Image = Shipment;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                trigger OnAction()
                begin
                    IpekPackageTransMgt.PackageCombineShip(Rec);
                end;
            }
            action(Receive)
            {
                Caption = 'Receive', Comment = 'TRK="Teslim Al"';
                Enabled = (Rec."Target Location" <> '') and (Rec.Shipped) and (not Rec.Received);
                Image = ReceiveLoaner;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Receive action.';
                trigger OnAction()
                var
                    PackageTransferHeader: Record "Package Transfer Header IPK";
                    IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";
                begin
                    PackageTransferHeader.Get(IpekPackageTransMgt.CreatePackageTransferDocumentFromPackageCombineHeader(Rec));
                    IpekPackageTransMgt.CreateAndPostItemReclassificationJournal(PackageTransferHeader, false);
                    Rec.Received := true;
                end;
            }
            action(Combine)
            {
                Caption = 'Combine', Comment = 'TRK="Birleştir"';
                Enabled = (Rec.Received) and (Rec."Target Package No." <> '') and (not Rec.Combined);
                Image = CopyBOMVersion;
                Promoted = true;
                PromotedIsBig = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Executes the Combine action.';

                trigger OnAction()
                begin
                    IpekPackageTransMgt.PackageCombineCombine(Rec);
                end;
            }





        }
    }
    var
        IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";


}