table 60027 "DeletedPackageNoInfoLines IPK"
{
    Caption = 'DeletedPackageNoInfoLines';
    DataClassification = CustomerContent;
    LookupPageId = "DeletedPackageNoInfoLines IPK";
    DrillDownPageId = "DeletedPackageNoInfoLines IPK";
    fields
    {
        field(1; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            NotBlank = true;
            AllowInCustomizations = Always;

            TableRelation = Item;
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get(Rec."Item No.") then begin
                    Rec.Description := Item.Description;
                    Rec."Unit of Measure Code" := Item."Base Unit of Measure";
                end;
            end;
        }
        field(2; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if ItemVariant.Get("Item No.", "Variant Code") then
                    Rec.Description := ItemVariant.Description;
            end;
        }
        field(3; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(4; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
            MinValue = 0;
        }
        field(5; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(6; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(7; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Location Code IPK" where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(8; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ExtendedDatatype = Barcode;
            AllowInCustomizations = Always;
            NotBlank = true;
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(9; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(10; "Line Item No."; Code[20])
        {
            Caption = 'Line Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
            TableRelation = Item;
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get(Rec."Line Item No.") then begin
                    Rec."Variant Code" := '';
                    Rec."Unit of Measure Code" := Item."Base Unit of Measure";
                    Rec.Description := Item.Description;
                end

                else begin
                    Rec.Description := '';
                    Rec."Unit of Measure Code" := '';
                    Rec."Variant Code" := '';
                end;
            end;
        }
        field(11; "Line Variant Code"; Code[10])
        {
            Caption = 'Line Variant Code';
            ToolTip = 'Specifies the value of the Variant Code field.';
            TableRelation = "Item Variant".Code where("Item No." = field("Line Item No."));
            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if ItemVariant.Get(Rec."Line Item No.", Rec."Line Variant Code") then
                    Rec.Description := ItemVariant.Description
                else
                    Rec.Validate("Item No.");
            end;
        }
        field(12; "Total Qty. on Location"; Decimal)
        {
            Caption = 'Total Qty. on Location';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Line Item No."), "Variant Code" = field("Line Variant Code"), "Location Code" = field("Location Code"), "Lot No." = field("Lot No.")));
            ToolTip = 'Specifies the value of the Total Qty. on Location field.';
        }
        field(13; "Production Location"; Boolean)
        {
            Caption = 'Production Location';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location."Production Location IPK" where(Code = field("Location Code")));
            ToolTip = 'Specifies the value of the Production Location field.';
        }
        field(14; "Source Package No. IPK"; Code[50])
        {
            Caption = 'Source Package No.';
            ToolTip = 'Specifies the value of the Source Package No. field.';
            TableRelation = "Package No. Information"."Package No.";
        }
        field(15; "Last Adjustment"; DateTime)
        {
            Caption = 'Last Adjustment';
            ToolTip = 'Specifies the value of the Last Adjustment field.';
        }
    }
    keys
    {
        key(Key1; "Item No.", "Variant Code", "Package No.", "Line No.")
        {
            Clustered = true;
            SumIndexFields = "Quantity";
        }
    }
}
