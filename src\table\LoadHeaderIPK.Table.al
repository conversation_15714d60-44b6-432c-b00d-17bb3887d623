table 60000 "Load Header IPK"
{
    Caption = 'Load Header';
    DataClassification = CustomerContent;
    Extensible = true;
    DrillDownPageId = "Load List IPK";
    LookupPageId = "Load List IPK";

    fields
    {
        field(1; "Warehouse Shipment No."; Code[20])
        {
            Caption = 'Warehouse Shipment No.';
            TableRelation = "Warehouse Shipment Header"."No.";
            Editable = false;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            begin
                Clear(NoSeries);

                if Rec."No." <> xRec."No." then begin
                    IpekPamukSetup.GetRecordOnce();
                    NoSeries.TestManual(IpekPamukSetup."Load No. Series");
                    Rec."No. Series" := '';
                end;
            end;
        }
        field(3; "Posted Document No."; Code[20])
        {
            Caption = 'Posted Document No.';
            TableRelation = "Posted Whse. Shipment Header"."No.";
            Editable = false;
            ToolTip = 'Specifies the value of the Posted Document No. field.';
        }
        field(4; "Seal No."; Code[25])
        {
            Caption = 'Seal No.';
            ToolTip = 'Specifies the value of the Seal No. field.';
        }
        field(5; "Driver Name"; Text[100])
        {
            Caption = 'Driver Name';
            ToolTip = 'Specifies the value of the Driver Name field.';
        }
        field(6; "Driver Surname"; Text[100])
        {
            Caption = 'Driver Surname';
            ToolTip = 'Specifies the value of the Driver Surname field.';
        }
        field(7; "Domestic Shipping Agent Code"; Code[20])
        {
            Caption = 'Domestic Shipping Agent Code';
            TableRelation = "Shipping Agent"."Code";
            ToolTip = 'Specifies the value of the Domestic Shipping Agent Code field.';
        }
        field(8; "Truck License Plate"; Code[20])
        {
            Caption = 'Truck License Plate';
            ToolTip = 'Specifies the value of the Truck License Plate field.';
        }
        field(9; "Trailer License Plate"; Code[20])
        {
            Caption = 'Trailer License Plate';
            ToolTip = 'Specifies the value of the Trailer License Plate field.';
        }
        field(10; "Load Method"; Enum "Load Method IPK")
        {
            Caption = 'Load Method';
            ToolTip = 'Specifies the value of the Load Method field.';
            trigger OnValidate()
            begin
                Rec."Load Method Type" := '';
            end;
        }
        field(11; "Label Text"; Code[50])
        {
            Caption = 'Label Text';
            ToolTip = 'Specifies the value of the Label Text field.';
            trigger OnValidate()
            var
                PackageNoInformation: Record "Package No. Information";
            begin

                PackageNoInformation.SetRange("Package No.", Rec."Label Text");
                PackageNoInformation.FindFirst();

                if PackageNoInformation."Pallet IPK" then
                    IpekSalesManagement.ProcessLoadCardPalletBarcode(PackageNoInformation, Rec)

                else
                    IpekSalesManagement.OnAfterValidate_LabelText_LoadHeader(Rec);


            end;
        }
        field(12; "Load Method Type"; Code[10])
        {
            Caption = 'Load Method Type';
            TableRelation = "Load Method Type IPK".Code where("Load Method" = field("Load Method"));
            ToolTip = 'Specifies the value of the Load Method Type field.';
        }
        field(13; "Booking No."; Code[50])
        {
            Caption = 'Booking No.';
            ToolTip = 'Specifies the value of the Booking No. field.';
        }
        field(14; Notes; Text[1024])
        {
            Caption = 'Notes';
            ToolTip = 'Specifies the value of the Notes field.';
        }
        field(15; "Partial Loading"; Boolean)
        {
            Caption = 'Partial Loading';
            ToolTip = 'Specifies the value of the Partial Loading field.';
        }
        field(16; Mix; Boolean)
        {
            Caption = 'Mix';
            ToolTip = 'Specifies the value of the Mix field.';
        }
        field(17; "Starting Date-Time"; DateTime)
        {
            Caption = ' Starting Date-Time';
            Editable = false;
            ToolTip = 'Specifies the value of the  Starting Date-Time field.';
        }
        field(18; "Ending Date-Time"; DateTime)
        {
            Caption = 'Ending Date-Time';
            Editable = false;
            ToolTip = 'Specifies the value of the Ending Date-Time field.';
        }
        field(19; "Container No."; Code[25])
        {
            Caption = 'Container No.';
            ToolTip = 'Specifies the value of the Container No. field.';
        }
        field(20; Status; Enum "Load Status IPK")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the value of the Status field.';
            trigger OnValidate()
            begin
                IpekSalesManagement.OnAfterValidate_Status_LoadHeader(Rec);
            end;
        }
        field(21; "Total Net Weight"; Decimal)
        {
            Caption = 'Total Net Weight';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Load Planning Line IPK"."Line Net Weight" where("Warehouse Shipment No." = field("Warehouse Shipment No."),
                                                                               "Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Net Weight field.';
        }
        field(22; "Total Gross Weight"; Decimal)
        {
            Caption = 'Total Gross Weight';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Load Planning Line IPK"."Line Gross Weight" where("Warehouse Shipment No." = field("Warehouse Shipment No."),
                                                                               "Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Gross Weight field.';
        }
        field(23; "Total Volume"; Decimal)
        {
            Caption = 'Total Volume';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Load Planning Line IPK"."Line Volume" where("Warehouse Shipment No." = field("Warehouse Shipment No."),
                                                                               "Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Volume field.';
        }
        field(24; "Total Unit Quantity"; Integer)
        {
            Caption = 'Total Unit Quantity';
            ToolTip = 'Specifies the value of the Total Unit Quantity field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Load Planning Line IPK"."Line Unit Quantity" where("Warehouse Shipment No." = field("Warehouse Shipment No."),
                                                                                    "Document No." = field("No.")));
        }
        field(25; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
            trigger OnValidate()
            var
                WarehouseShipmentHeader: Record "Warehouse Shipment Header";
            begin
                WarehouseShipmentHeader.Get(Rec."Warehouse Shipment No.");
                WarehouseShipmentHeader.Validate("Posting Date", Rec."Posting Date");
                WarehouseShipmentHeader.Modify(true);
            end;
        }
        field(26; "Load Location"; Text[50])
        {
            Caption = 'Loading Location';
            ToolTip = 'Specifies the value of the Loading Location field.';
        }
        field(27; "Driver VAT Registration No."; Code[50])
        {
            Caption = 'Driver VAT Registration No.';
            ToolTip = 'Specifies the value of the Driver VAT Registration No. field.';
        }
        field(28; "E-Shipment No."; Code[20])
        {
            Caption = 'E-Shipment No.';
            ToolTip = 'Specifies the value of the E-Shipment No. field.';
            Editable = false;
        }
        field(29; "Driver No."; Code[20])
        {
            Caption = 'Driver No.';
            TableRelation = Employee."No.";
            ToolTip = 'Specifies the value of the Driver No. field.';
            trigger OnValidate()
            var
                Employee: Record Employee;
            begin
                if not Employee.Get(Rec."Driver No.") then begin
                    Rec.Validate("Driver Name", '');
                    Rec.Validate("Driver Surname", '');
                    Rec.Validate("Driver VAT Registration No.", '');
                end
                else begin
                    Rec.Validate("Driver Name", Employee."First Name");
                    Rec.Validate("Driver Surname", Employee."Last Name");
                    Rec.Validate("Driver VAT Registration No.", Employee."Turkish Citizenship No. INF");
                end;
            end;
        }
        field(30; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
            Editable = false;
        }
        field(31; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            ToolTip = 'Specifies the value of the Customer Name field.';
            Editable = false;
        }
        field(32; "Ship-to Code"; Code[10])
        {
            Caption = 'Ship-to Code';
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Customer No."));
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Code field.';
        }
        field(33; "Ship-to Name"; Text[100])
        {
            Caption = 'Ship-to Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Ship-to Name field.';
        }

        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
        field(34; "Last Read Barcode"; Code[50])
        {
            Caption = 'Last Read Barcode';
            ToolTip = 'Specifies the value of the Last Read Barcode field.';

        }
        field(35; "Last Read Quantity"; Decimal)
        {
            Caption = 'Last Read Quantity';
            ToolTip = 'Specifies the value of the Last Read Quantity field.';

        }
    }
    keys
    {
        key(PK; "Warehouse Shipment No.", "No.")
        {
            Clustered = true;
        }
        key(key2; "Posted Document No.")
        {
        }
    }

    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
        NoSeries: Codeunit "No. Series";

    trigger OnDelete()
    var
        LoadLine: Record "Load Line IPK";
        LoadPlanningLine: Record "Load Planning Line IPK";
        NoBarcodeFoundErr: Label 'There is barcodes in this load card, please delete those manually';
    begin

        Rec.TestField("E-Shipment No.", '');

        LoadLine.SetRange("Warehouse Shipment No.", Rec."Warehouse Shipment No.");
        LoadLine.SetRange("Document No.", Rec."No.");
        if not LoadLine.IsEmpty() then
            Error(NoBarcodeFoundErr);
        // LoadLine.DeleteAll(true);

        LoadPlanningLine.SetRange("Warehouse Shipment No.", Rec."Warehouse Shipment No.");
        LoadPlanningLine.SetRange("Document No.", Rec."No.");
        LoadPlanningLine.DeleteAll(true);
    end;

    trigger OnInsert()
    var
        WarehouseShipmentHeader: Record "Warehouse Shipment Header";
    begin
        Clear(NoSeries);

        if Rec."No." = '' then begin
            IpekPamukSetup.GetRecordOnce();
            IpekPamukSetup.TestField("Load No. Series");
            Rec."No. Series" := IpekPamukSetup."Load No. Series";
            if NoSeries.AreRelated(IpekPamukSetup."Load No. Series", xRec."No. Series") then
                Rec."No. Series" := xRec."No. Series";

            Rec."No." := NoSeries.GetNextNo(IpekPamukSetup."Load No. Series");
        end;

        WarehouseShipmentHeader.Get(Rec."Warehouse Shipment No.");
        WarehouseShipmentHeader.TestField("Transport Means EHSP INF");

        Rec.Validate("Customer No.", WarehouseShipmentHeader."Sell-to Source No. INF");
        Rec.Validate("Customer Name", WarehouseShipmentHeader."Sell-to Customer Name INF");
        Rec.Validate("Posting Date", WarehouseShipmentHeader."Posting Date");
        Rec.Validate("Ship-to Code", WarehouseShipmentHeader."Ship-to Code INF");
        Rec.Validate("Ship-to Name", WarehouseShipmentHeader."Ship-to Name INF");
        Rec.Validate(Mix, true);
        Rec.Validate("Load Location", IpekPamukSetup."Def. Load Location");
    end;
}