tableextension 60059 "Package No. Information MXW" extends "Package No. Information"
{
    fields
    {
        field(60000; "Lot No. MXW"; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number associated with this package.';
            DataClassification = CustomerContent;
        }
        field(60003; "Document No. MXW"; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the document number associated with this package.';
            DataClassification = CustomerContent;
        }
        field(60001; "Location Code MXW"; Code[10])
        {
            Caption = 'Location Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Ledger Entry"."Location Code" where("Item No." = field("Item No."),
                                                                  "Variant Code" = field("Variant Code"),
                                                                  "Package No." = field("Package No."),
                                                                  Open = const(true)));
            ToolTip = 'Specifies the value of the Location Code field.';
        }
    }
}
