# Maxwell Customizations

## Overview
Custom modifications and enhancements for Maxwell's Business Central environment.

## Project Information
- **Publisher**: MXW
- **Version**: 1.0.0.0
- **Business Central Version**: 25.0
- **Extension Type**: Per-Tenant Extension (PTE)
- **ID Range**: 60000-60999

## Development Environment
- **Environment Name**: Maxwell-Sandbox
- **Tenant ID**: a952e718-6eca-46c7-b922-6943a942f71b

## Project Structure
```
src/
├── Codeunits/          # Business logic and procedures
├── PageExtensions/     # Extensions to existing pages
├── Pages/             # New custom pages
├── Reports/           # Custom reports
├── TableExtensions/   # Extensions to existing tables
└── Tables/            # New custom tables
```

## Naming Conventions
- Use PascalCase for all object names, fields, and variables
- Since no specific affix is required for this PTE, use clear, descriptive names
- Follow the namespace structure: `MXW.Maxwell.FeatureName`
- Object IDs should be within the range 60000-60999

## Development Guidelines
1. Always use proper namespaces
2. Follow the established folder structure
3. Use meaningful object and variable names
4. Include proper documentation in code
5. Test thoroughly before deployment

## Setup Instructions
1. Download symbols for Business Central 25.0
2. Set up connection to Maxwell-Sandbox environment
3. Ensure proper tenant authentication

## Analyzer Configuration
The project is configured with:
- CodeCop (enabled)
- PerTenantExtensionCop (enabled)
- UICop (enabled)
- Custom ruleset based on Stefan Maron's PTE recommendations

Note: AppSourceCop is intentionally disabled as this is a PTE project.
