page 60070 "Include Adjustment IPK"
{
    ApplicationArea = All;
    Caption = 'Include Adjustment ';
    PageType = StandardDialog;

    layout
    {
        area(Content)
        {
            field(InventoryAdjustmentNo; InventoryAdjustmentNo)
            {
                Caption = 'Inventory Adjustment No';
                ToolTip = 'Specifies the value of the Start Date field.';
            }
            field(MergeLocationCode; MergeLocationCode)
            {
                Caption = 'Merge Location Code';
                ToolTip = 'Specifies the value of the Merge Document No. field.';
            }
        }
    }

    var
        MergeLocationCode: Code[10];
        InventoryAdjustmentNo: Code[20];

    procedure GetInventoryAdjustmentNo(): Code[20]
    begin
        exit(InventoryAdjustmentNo);
    end;


    procedure GetMergeLocationCode(): Code[10]
    begin
        exit(MergeLocationCode);
    end;
}