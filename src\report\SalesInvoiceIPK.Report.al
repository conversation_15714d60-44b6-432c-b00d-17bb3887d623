report 60001 "Sales Invoice IPK"
{
    ApplicationArea = All;
    Caption = 'Sales Invoice';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(SalesHeader; "Sales Header")
        {
            CalcFields = "Total Gross Weight IPK", "Total Net Weight IPK", "Total Volume IPK", "Total Pack IPK";
            column(DocumentNo; "No.")
            {
            }
            column(ShippingNo; "Shipping No.")
            {
            }
            column(DocumentDate; "Document Date")
            {
            }
            column(SelltoCustomerNo; "Sell-to Customer No.")
            {
            }
            column(SelltoCustomername; "Sell-to Customer Name")
            {
            }
            column(SelltoAddress; "Sell-to Address")
            {
            }
            column(SelltoAddress2; "Sell-to Address 2")
            {
            }
            column(SelltoCity; "Sell-to City")
            {
            }
            column(SelltoCounty; "Sell-to County")
            {
            }

            // column(BilltoAddress; "Bill-to Address")
            // {
            // }
            // column(BilltoAddress2; "Bill-to Address 2")
            // {
            // }
            // column(BilltoCounty; "Bill-to County")
            // {
            // }
            // column(BilltoCity; "Bill-to City")
            // {
            // }
            column(ShipmentMethodCode; "Shipment Method Code")
            {
            }
            column(ShipmentDate; "Shipment Date")
            {
            }
            column(ShipToAddress; "Ship-to Address")
            {
            }
            column(ShiptoAddress2; "Ship-to Address 2")
            {
            }
            column(ShiptoCity; "Ship-to City")
            {
            }
            column(ShiptoCounty; "Ship-to County")
            {
            }
            column(Amount; Amount)
            {
            }
            column(TotalNetWeight; "Total Net Weight IPK")
            {
            }
            column(TotalGrossWeight; "Total Gross Weight IPK")
            {
            }
            column(CurrencyCode; "Currency Code")
            {
            }
            column(TotalVolume; "Total Volume IPK")
            {
            }
            column(TotalPackQuantity; "Total Pack IPK")
            {
            }
            #region Captions
            column(DocumentNoCaption; FieldCaption("No."))
            {
            }
            column(ShippingNoCaption; FieldCaption("Shipping No."))
            {
            }
            column(DocumentDateCaption; FieldCaption("Document Date"))
            {
            }
            column(SelltoCustomerNoCaption; FieldCaption("Sell-to Customer No."))
            {
            }
            column(SelltoCustomernameCaption; FieldCaption("Sell-to Customer Name"))
            {
            }
            column(SelltoAddressCaption; FieldCaption("Sell-to Address"))
            {
            }
            column(SelltoAddress2Caption; FieldCaption("Sell-to Address 2"))
            {
            }
            column(SelltoCityCaption; FieldCaption("Sell-to City"))
            {
            }

            // column(BilltoAddressCaption; FieldCaption("Bill-to Address"))
            // {
            // }
            // column(BilltoAddress2Caption; FieldCaption("Bill-to Address 2"))
            // {
            // }
            // column(BilltoCityCaption; FieldCaption("Bill-to City"))
            // {
            // }
            column(TransportMethodCaption; FieldCaption("Transport Method"))
            {
            }
            column(ShiptoCityCaption; FieldCaption("Ship-to City"))
            {
            }
            column(ShiptoCountyCaption; FieldCaption("Ship-to County"))
            {
            }
            column(PaymentTermsCodeCaption; FieldCaption("Payment Terms Code"))
            {
            }
            column(TotalNetWeightCaption; FieldCaption("Total Net Weight IPK"))
            {
            }
            column(TotalGrossWeightCaption; FieldCaption("Total Gross Weight IPK"))
            {
            }

            #endregion
            // dataitem("Bill-to Country/Region Translation"; "Country/Region Translation")
            // {
            //     DataItemLink = "Country/Region Code" = field("Sell-to Country/Region Code"), "Language Code" = field("Language Code");
            //     column(BilltoCityCountry; Name)
            //     {
            //     }
            //     #region Captions
            //     column(BilltoCityCountryCaption; FieldCaption(Name))
            //     {
            //     }
            //     #endregion
            // }
            // dataitem("Sell-to Country/Region Translation"; "Country/Region Translation")
            // {
            //     DataItemLink = "Country/Region Code" = field("Sell-to Country/Region Code"), "Language Code" = field("Language Code");
            //     column(SelltoCityCountry; Name)
            //     {
            //     }
            //     #region Captions
            //     column(SelltoCityCountryCaption; FieldCaption(Name))
            //     {
            //     }
            //     #endregion
            // }
            dataitem("Payment Term Translation"; "Payment Term Translation")
            {
                DataItemLink = "Payment Term" = field("Payment Terms Code"), "Language Code" = field("Language Code");
                column(PaymentTermsDescription; Description)
                {
                }
                #region Captions
                column(PaymentTermsDescriptionCaption; FieldCaption(Description))
                {
                }
                #endregion
            }
            dataitem(CompanyBankAccountCode; "Bank Account")
            {
                DataItemLink = "No." = field("Company Bank Account Code"), "Currency Code" = field("Currency Code");
                column(Name; Name)
                {
                }
                column(SWIFTCode; "SWIFT Code")
                {
                }
                column(BankAccountNo; "Bank Account No.")
                {
                }
                column(Branch_Code_INF; "Branch Code INF")
                {
                }
                column(Branch_Name_INF; "Branch Name INF")
                {
                }
                column(IBAN; IBAN)
                {
                }
                #region Captions
                column(NameCaption; FieldCaption(Name))
                {
                }
                column(SWIFTCodeCaption; FieldCaption("SWIFT Code"))
                {
                }
                column(BankAccountNoCaption; FieldCaption("Bank Account No."))
                {
                }
                column(IBANCaption; FieldCaption(IBAN))
                {
                }
                #endregion

            }
            dataitem("Sales Line"; "Sales Line")
            {
                DataItemLink = "Document Type" = field("Document Type"), "Document No." = field("No.");
                DataItemTableView = where(Type = const(Item));
                column(NoSalesInvoiceLine; "No.")
                {
                }
                column(ItemReferenceNoSalesInvoiceLine; "Item Reference No.")
                {
                }
                column(DescriptionSalesInvoiceLine; Description)
                {
                }
                column(QuantitySalesInvoiceLine; Quantity)
                {
                }
                column(UnitPriceSalesInvoiceLine; "Unit Price")
                {
                }
                column(AmountSalesInvoiceLine; Amount)
                {
                }
                column(NetWeightSalesInvoiceLine; "Net Weight")
                {
                }
                column(GrossWeightSalesInvoiceLine; "Gross Weight")
                {
                }
                column(UnitofMeasureSalesInvoiceLine; "Unit of Measure")
                {
                }
                column(UnitofMeasureCodeSalesInvoiceLine; "Unit of Measure Code")
                {
                }
                column(ItemReferenceUnitofMeasureSalesInvoiceLine; "Item Reference Unit of Measure")
                {
                }
                column(LineUnitQuantityIPK; "Line Unit Quantity IPK")
                {
                }
                column(UnitofMeasureCode; "Unit of Measure Code")
                {
                }
                column(LineUnitQuantityIPKSalesLineLbl; LineUnitQuantityIPKSalesLineLbl)
                {
                }
                column(Unit_of_Measure_Code; "Unit of Measure Code")
                {
                }
                column(Unit_of_Measure; "Unit of Measure")
                {
                }
                column(Tariff_No; "Tariff No. INF")
                {
                }
                column(CFR_Piece_Unit_Price; "Freight Inc PieceUnitPrice IPK")
                {
                }
                column(CFR_Line_Amount; "FOB Line Amount IPK")
                {
                }

                #region Captions
                column(NoSalesInvoiceLineCaption; FieldCaption("No."))
                {
                }
                column(ItemReferenceNoSalesInvoiceLineCaption; FieldCaption("Item Reference No."))
                {
                }
                column(DescriptionSalesInvoiceLineCaption; FieldCaption(Description))
                {
                }
                column(QuantitySalesInvoiceLineCaption; FieldCaption(Quantity))
                {
                }
                column(UnitPriceSalesInvoiceLineCaption; FieldCaption("Unit Price"))
                {
                }
                column(AmountSalesInvoiceLineCaption; FieldCaption(Amount))
                {
                }
                column(NetWeightSalesInvoiceLineCaption; FieldCaption("Net Weight"))
                {
                }
                column(GrossWeightSalesInvoiceLineCaption; FieldCaption("Gross Weight"))
                {
                }
                column(UnitofMeasureSalesInvoiceLineCaption; FieldCaption("Unit of Measure"))
                {
                }
                column(UnitofMeasureCodeSalesInvoiceLineCaption; FieldCaption("Unit of Measure Code"))
                {
                }
                column(ItemReferenceUnitofMeasureSalesInvoiceLineCaption; FieldCaption("Item Reference Unit of Measure"))
                {
                }
                column(LineUnitQuantityIPKCaption; FieldCaption("Line Unit Quantity IPK"))
                {
                }
                column(UnitofMeasureCodeCaption; FieldCaption("Unit of Measure Code"))
                {
                }
                column(LineUnitQuantityIPKSalesLineLblCaption; LineUnitQuantityIPKSalesLineLbl)
                {
                }

                #endregion


                dataitem(Item; Item)
                {
                    DataItemLink = "No." = field("No.");
                    column(PiecesonPalletIPK; "Pieces on Pallet IPK")
                    {
                    }
                    #region Captions
                    column(PiecesonPalletIPKCaption; FieldCaption("Pieces on Pallet IPK"))
                    {
                    }
                    column(TariffNo_Item; "Tariff No.")
                    {
                    }
                    #endregion
                }
            }

        }
    }
    requestpage
    {
        layout
        {

        }
        actions
        {
            area(Processing)
            {
            }
        }
    }
    var
        LineUnitQuantityIPKSalesLineLbl: Label 'Carton';
}