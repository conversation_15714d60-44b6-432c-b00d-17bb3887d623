tableextension 60040 "Production BOM Header IPK" extends "Production BOM Header"
{
    fields
    {
        field(60000; "Approval Status IPK"; Enum "Approval Status")
        {
            Caption = 'Approval Status';
            ToolTip = 'Specifies the value of the Approval Status field.';
            DataClassification = ToBeClassified;
            trigger OnValidate()
            var
                IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
                IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
                IpekEvents: Codeunit "Ipek Events IPK";
                Vartest: Text;
            begin
                if /*(xRec."Approval Status IPK" = xRec."Approval Status IPK"::" ")and*/
                 (Rec."Approval Status IPK" = Rec."Approval Status IPK"::Open) then begin
                    IpekPamukSetup.GetRecordOnce();
                    Vartest := GetUrl(CurrentClientType(), CurrentCompany() + '&', ObjectType::Page, 99000786, Rec);
                    //Temp Solution to GetUrl errors,gite ticket açmışlar bununla ilgil
                    Vartest := Vartest.Replace('%26', '&');
                    Vartest := Vartest.Replace('99000786', '99000786&');


                    IpekEvents.ProductionBOMApprovalRequest(Rec."No.",
                    IpekBasicFunctions.GetUserNameFromSecurityId(Rec.SystemModifiedBy),
#pragma warning disable AA0139
Vartest,
                    IpekPamukSetup."BOM Change Mail Group");
                    // Rec.CopyLinks();
                    // Message(Vartest);
                end;
            end;
        }
        field(60001; "Category Code IPK"; Code[20])
        {
            Caption = 'Category Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Category Code field.';
            FieldClass = FlowField;
            CalcFormula = lookup("Item"."Item Category Code" where("No." = field("No.")));
        }
        field(60002; "Category Description IPK"; Text[100])
        {
            Caption = 'Category Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Category Description field.';
            FieldClass = FlowField;
            CalcFormula = lookup("Item Category".Description where(Code = field("Category Code IPK")));
        }
    }
}