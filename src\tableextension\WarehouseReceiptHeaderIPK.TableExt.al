tableextension 60011 "Warehouse Receipt Header IPK" extends "Warehouse Receipt Header"
{
    fields
    {
        field(60000; "Vendor No. IPK"; Code[20])
        {
            Caption = 'Vendor No.';
            ToolTip = 'Specifies the vendor number.';
            TableRelation = Vendor."No.";
            trigger OnValidate()
            var
                Vendor: Record Vendor;
            begin
                CalcFields("Vendor Name IPK");
                if Vendor.Get(Rec."Vendor No. IPK") then
                    Rec."Location Code" := Vendor."Location Code";
            end;
        }
        field(60001; "Vendor Name IPK"; Text[100])
        {
            Caption = 'Vendor Name';
            ToolTip = 'Specifies the vendor name.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Vendor No. IPK")));
        }
        field(60002; "Quality Control Not Proc. IPK"; Boolean)
        {
            FieldClass = FlowField;
            Caption = 'Quality Control Not Processed';
            ToolTip = 'Specifies the value of the Quality Control Not Processed IPK field.';
            AllowInCustomizations = Always;
            Editable = false;
            CalcFormula = exist("Warehouse Receipt Line" where("No." = field("No."), "Quality Control Doc. Stat. IPK" = filter("Input Pending" | " ")));
        }
        modify("Vendor Shipment No.")
        {
            trigger OnAfterValidate()
            begin
                IpekPurchaseManagement.WareHouseRecipt_VendorShipmentNo_OnAfterValidate(Rec);
            end;
        }

    }
    var
        IpekPurchaseManagement: Codeunit "Ipek Purchase Management IPK";
}