page 60059 "Inventory Adjustment Item List"
{
    ApplicationArea = All;
    Caption = 'Inventory Adjustment Item List';
    PageType = List;
    SourceTable = "Inventory Adjustment Item List";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Line No."; Rec."Line No.")
                {
                    Visible = false;
                    ToolTip = 'Specifies the value of the Line No. field.';
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the value of the Item No. field.';
                    TableRelation = Item."No.";
                }
            }
        }
    }
}