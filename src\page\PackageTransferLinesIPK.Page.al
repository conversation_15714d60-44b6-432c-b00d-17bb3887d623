page 60067 "Package Transfer Lines IPK"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Lines';
    PageType = List;
    SourceTable = "Package Transfer Line IPK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Quantity To Transfer"; Rec."Quantity To Transfer")
                {
                }
                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                }
                field("Total Qty. on Location"; Rec."Total Qty. on Location")
                {
                }
                field("Total Qty. on Loc. Trans"; Rec."Total Qty. on Loc. Trans")
                {
                    ToolTip = 'Specifies the value of the Total Qty. on Loc. Pack" field.';
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                }
                field(Received; Rec.Received)
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Package Creation Date"; Rec."Package Creation Date")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
                field("Current Package Location"; Rec."Current Package Location")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(AsignToInventoryAdjustment)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Asign To Inventory Adjustment field.';
                Caption = 'Asign To Inventory Adjustment', Comment = 'TRK="YourLanguageCaption"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = AssessFinanceCharges;

                trigger OnAction()
                var
                    InventoryAdjustmentHeader: Record "Inventory Adjustment Header";
                    PackageTransferLine: Record "Package Transfer Line IPK";
                    IncludeAdjustment: Page "Include Adjustment IPK";
                    InventoryAdjustmentLocation: Code[10];
                    InventoryAdjustmentNo: Code[20];

                begin
                    IncludeAdjustment.LookupMode(true);
                    if IncludeAdjustment.RunModal() = Action::LookupOK then begin
                        InventoryAdjustmentLocation := IncludeAdjustment.GetMergeLocationCode();
                        InventoryAdjustmentNo := IncludeAdjustment.GetInventoryAdjustmentNo();
                        InventoryAdjustmentHeader.Get(InventoryAdjustmentNo);
                        SetSelectionFilter(PackageTransferLine);
                        PackageTransferLine.CalcFields("Current Package Location");
                        PackageTransferLine.SetRange("Current Package Location", InventoryAdjustmentLocation);
                        PackageTransferLine.FindSet();

                        repeat
                            InventoryAdjustmentHeader.Validate(Barcode, PackageTransferLine."Package No.");
                        until PackageTransferLine.Next() = 0;
                        Message(Format(PackageTransferLine.Count()));
                    end;
                end;
            }
        }
    }
}