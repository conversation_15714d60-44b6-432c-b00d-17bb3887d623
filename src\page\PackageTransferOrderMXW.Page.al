page 60022 "Package Transfer Order MXW"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Order';
    PageType = Document;
    SourceTable = "Package Transfer Header MXW";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = not Rec.Received and not Rec.Shipped;

                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    QuickEntry = false;
                }


                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                    ApplicationArea = All;
                    Editable = false;
                }

                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    ApplicationArea = All;
                    QuickEntry = false;
                    ToolTip = 'Specifies the value of the Transfer-to Code field.';

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }

                field("Posting Date"; Rec."Posting Date")
                {
                    ApplicationArea = All;
                    QuickEntry = false;
                }

                field(Shipped; Rec.Shipped)
                {
                    ApplicationArea = All;
                    QuickEntry = false;
                }

                field(Received; Rec.Received)
                {
                    ApplicationArea = All;
                    QuickEntry = false;
                }

                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                    ApplicationArea = All;
                }

                // field("Is Production Location"; Rec."Is Production Location")
                // {
                //     ApplicationArea = All;
                // }
            }

            group(BarcodeReadingArea)
            {
                Caption = 'Barcode Reading';
                Editable = not Rec.Received and not Rec.Shipped;

                field(Barcode; Rec.Barcode)
                {
                    ApplicationArea = All;
                    ShowCaption = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
            }

            part(Lines; "Package Transfer Subpage MXW")
            {
                Caption = 'Lines';
                Editable = not Rec.Received and not Rec.Shipped;
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ShipAndReceive)
            {
                Caption = 'Ship & Receive';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Post;
                ToolTip = 'Executes the Ship & Receive action.';
                Enabled = not Rec.Received;
                PromotedOnly = true;
                //Visible = not Rec."Is Production Location";

                trigger OnAction()
                begin
                    MaxwellPackageTransMgt.ShipAndReceivePackageTransferHeader(Rec);
                end;
            }

            action(Ship)
            {
                Caption = 'Ship';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Shipment;
                ToolTip = 'Executes the Ship action.';
                Enabled = not Rec.Shipped and not Rec.Received;
                //Visible = Rec."Is Production Location";
                PromotedOnly = true;

                trigger OnAction()
                begin
                    Rec.Validate(Shipped, true);
                    Rec.Modify(true);
                end;
            }

            action(Receive)
            {
                Caption = 'Receive';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReceiveLoaner;
                ToolTip = 'Executes the Receive action.';
                Enabled = Rec.Shipped and not Rec.Received;
                //Visible = Rec."Is Production Location";
                PromotedOnly = true;

                trigger OnAction()
                begin
                    MaxwellPackageTransMgt.CreateAndPostItemReclassificationJournal(Rec, false);
                end;
            }
        }
    }

    var
        MaxwellPackageTransMgt: Codeunit "Maxwell Package Trans. Mgt MXW";
}
