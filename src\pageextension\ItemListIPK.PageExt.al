pageextension 60025 "Item List IPK" extends "Item List"
{
    layout
    {
        addafter("Item Category Code")
        {
            field("Item Category Description IPK"; Rec."Item Category Description IPK")
            {
                ApplicationArea = All;
            }
        }
        addafter(InventoryField)
        {

            field("Net Change IPK"; Rec."Net Change")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Net Change field.';
            }
        }
    }
}