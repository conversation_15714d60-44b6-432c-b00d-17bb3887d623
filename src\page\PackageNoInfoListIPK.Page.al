page 60046 "Package No. Info. List IPK"
{
    ApplicationArea = All;
    Caption = 'Package No. Info. List';
    PageType = ListPart;
    SourceTable = "Package No. Information";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    ToolTip = 'Specifies the customs declaration number.';
                    // Visible = ShowDetails;
                }
                field("Line Item No. IPK"; Rec."Line Item No. IPK")
                {
                    // Visible = ShowDetails;
                }
                field("Line Variant Code IPK"; Rec."Line Variant Code IPK")
                {
                    // Visible = ShowDetails;
                }
                field("Last Adjustment IPK"; Rec."Last Adjustment IPK")
                {
                    // Visible = ShowDetails;
                }
            }
        }
    }


}