pageextension 60020 "Package No. Information List" extends "Package No. Information List"
{
    Editable = true;//Silinecek
    layout
    {
        modify(Description)
        {
            Visible = false;
        }
        modify(Blocked)
        {
            Visible = false;
        }

        modify("Item No.")
        {
            Visible = false;
        }
        modify("Country/Region Code")
        {
            Visible = false;
        }
        addafter(Description)
        {
            field("Line Item No IPK"; Rec."Line Item No. IPK")
            {
                ApplicationArea = All;
            }
            field("Line Variant Code IPK"; Rec."Line Variant Code IPK")
            {
                ApplicationArea = All;
            }
            // field("Line Item Description IPK"; Rec."Line Item Description IPK")
            // {
            //     ApplicationArea = All;
            // }
            field("Total Quantity IPK"; Rec."Total Quantity IPK")
            {
                ApplicationArea = All;
            }
            field("Location Code IPK"; Rec."Location Code IPK")
            {
                ApplicationArea = All;
                Visible = ShowDetails;
            }
            field("Document No. IPK"; Rec."Document No. IPK")
            {
                ApplicationArea = All;
            }
            field("Produced By IPK"; Rec."Produced By IPK")
            {
                ApplicationArea = All;
                Visible = ShowDetails;
            }
            field("Sales Order No. IPK"; Rec."Sales Order No. IPK")
            {
                ApplicationArea = All;
            }
            field("Warehouse Shipment No. IPK"; Rec."Warehouse Shipment No. IPK")
            {
                ApplicationArea = All;
                Visible = ShowDetails;
            }
            field("Load No. IPK"; Rec."Load No. IPK")
            {
                ApplicationArea = All;
                Visible = ShowDetails;
            }
            field("Created At IPK"; Rec."Created At IPK")
            {
                ApplicationArea = All;
            }
            field("Pallet IPK"; Rec."Pallet IPK")
            {
                ApplicationArea = All;
                Visible = ShowDetails;
            }
            field("Last Adjustment IPK"; Rec."Last Adjustment IPK")
            {
                ApplicationArea = All;
                Visible = ShowDetails;
            }
            field("Flagged For Delete IPK"; Rec."Flagged For Delete IPK")
            {
                ApplicationArea = All;
                Visible = ShowDetails;
            }
        }
    }
    actions
    {
        addfirst(Reporting)
        {
            action("ShowDetails IPK")
            {
                ApplicationArea = All;
                Caption = 'Show Details', Comment = 'TRK="Detaylaylı Görünüm"';
                Image = ViewDetails;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Show Details action.';

                trigger OnAction()
                begin
                    ShowDetails := not ShowDetails;
                end;
            }
            action("PrintLabel IPK")
            {
                ApplicationArea = All;
                Caption = 'Print Label', Comment = 'TRK="Etiket Yazdır"';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Executes the Print Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);

                    Report.Run(Report::"Palette Label IPK", false, false, PackageNoInformation);
                end;
            }
            action("Delete Selected IPK")
            {
                ApplicationArea = All;
                Caption = 'Delete Selected With Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Delete Selected action.';
                PromotedOnly = true;
                // Visible = false;
                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin

                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PackageNoInformation.FindSet();
                    IpekBasicFunctions.DeletePackageFromHeader(PackageNoInformation);

                end;
            }
            action("Negative Adjust IPK")
            {
                ApplicationArea = All;
                Caption = 'Negative Adjust';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Delete Selected action.';
                PromotedOnly = true;
                // Visible = false;
                trigger OnAction()
                var
                    PackageNoInfoLine: Record "Package No. Info. Line IPK";
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PackageNoInformation.FindSet(false);
                    repeat
                        PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
                        if PackageNoInfoLine.FindSet(false) then
                            repeat
                                IpekBasicFunctions.CreateItemJournalLineBeforeDeletion(PackageNoInfoLine);
                            until PackageNoInfoLine.Next() = 0;
                    until PackageNoInformation.Next() = 0;
                    Message('Done');
                end;
            }
            // action("Delete And Adjust Inventory IPK")
            // {
            //     ApplicationArea = All;
            //     Caption = 'Delete And Adjust Inventory';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = PostOrder;
            //     ToolTip = 'Executes the Create Delete And Adjust Inventory action.';
            //     PromotedOnly = true;
            //     // Visible = false;
            //     trigger OnAction()
            //     var
            //         PackageNoInformation: Record "Package No. Information";
            //         InventoryAdjustment: Codeunit "Inventory Adjustment IPK";
            //     begin
            //         CurrPage.SetSelectionFilter(PackageNoInformation);
            //         PackageNoInformation.FindSet();
            //         InventoryAdjustment.TransferPackNoToInventoryAdjustment(PackageNoInformation);
            //     end;
            // }



        }
    }



    var
        IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
        ShowDetails: Boolean;

    trigger OnOpenPage()
    begin
        Rec.SetCurrentKey(SystemCreatedAt);
        Rec.Ascending(false);
    end;
}