pageextension 60014 "Whse. Receipt Subform IPK" extends "Whse. Receipt Subform"
{

    layout
    {
        addafter(Description)
        {
            field("Package Count IPK"; Rec."Package Count IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Package Count field.';
            }
            field("Total Package Quantity IPK"; Rec."Total Package Quantity IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Total Package Quantity field.';
            }
            field("Lot No. IPK"; Rec."Lot No. IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Lot No. field.';
            }
            field("Item Tracking Info Assignd IPK"; Rec."Item Tracking Info Assignd IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Item Tracking Info. Assigned field.';
            }
            field("Quality Control Document No. IPK"; Rec."Quality Control Doc. No. IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Quality Control Document No. field.';
                trigger OnDrillDown()
                var
                    QualityControlHeaderQCM: Record "Quality Control Header QCM";
                begin
                    QualityControlHeaderQCM.Get(Rec."Quality Control Doc. No. IPK");
                    PageManagement.PageRun(QualityControlHeaderQCM);
                end;
            }
            field("Quality Control Doc. Stat. IPK"; Rec."Quality Control Doc. Stat. IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Quality Control Document Status field.';
            }
            field("Quality Control Documents IPK"; Rec."Quality Control Documents IPK")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Quality Control Documents field.';
            }
        }
        modify("Variant Code")
        {
            Visible = true;
        }
    }
    actions
    {
        addfirst("&Line")
        {
            action("CreatePackage IPK")
            {
                ApplicationArea = All;
                Caption = 'Create Package';
                Image = Action;
                ToolTip = 'Executes the Create Package action.';

                trigger OnAction()
                begin
                    IpekPurchaseManagement.CreatePackageFromWarehouseReceiptLine(Rec, Enum::"Package Creation Method IPK"::Single);
                end;
            }
            action("Assign Item Tracking Info. IPK")
            {
                ApplicationArea = All;
                Caption = 'Assign Item Tracking Info.';
                Image = Action;
                ToolTip = 'Executes the Assign Item Tracking Info. action.';

                trigger OnAction()
                begin
                    //IpekPurchaseManagement.CreatePackageFromWarehouseReceiptLine(Rec, Enum::"Package Creation Method IPK"::Single);
                    IpekPurchaseManagement.AssignItemTrackingInformationFromWarehouseReceiptLine(Rec);
                end;
            }
            action("Generate Quality Control Line IPK")
            {
                ApplicationArea = All;
                Caption = 'Generate Quality Control Line';
                Image = LotInfo;

                Visible = false;
                // Promoted = true;
                // PromotedCategory = Process;
                // PromotedIsBig = true;
                ToolTip = 'Executes the Generate Quality Control Line action.';

                trigger OnAction()
                begin
                    IpekQualityManagementIPK.GenerateQualityControlDocumentFromWareHauseRecptLine(Rec);
                end;
            }
        }
    }
    var

        IpekPurchaseManagement: Codeunit "Ipek Purchase Management IPK";
        IpekQualityManagementIPK: Codeunit "Ipek Quality Management IPK";
        PageManagement: Codeunit "Page Management";
}