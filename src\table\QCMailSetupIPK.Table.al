table 60029 "Q.C. Mail Setup IPK"
{
    Caption = 'QC Mail Map';
    DataClassification = ToBeClassified;

    DrillDownPageId = "Q.C. Mail Setup IPK";
    LookupPageId = "Q.C. Mail Setup IPK";
    fields
    {
        field(1; EntryNo; Integer)
        {
            Caption = 'EntryNo';
            AutoIncrement = true;
            AllowInCustomizations = Always;
        }
        field(2; "Quality Control Status"; Enum "Quality Control Status QCM")
        {
            Caption = 'Quality Control Status';
            ToolTip = 'Specifies the value of the Quality Control Status field.';
        }
        field(3; "E-Mail"; Text[100])
        {
            Caption = 'E-Mail';
            ToolTip = 'Specifies the value of the E-Mail field.';
        }
        // field(4; User; )
        // {
        //     Caption = 'User';

        // }
    }
    keys
    {
        key(PK; EntryNo)
        {
            Clustered = true;
        }
    }
}