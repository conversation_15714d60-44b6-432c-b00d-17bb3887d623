report 60009 "GEKAP Dataset IPK"
{
    ApplicationArea = All;
    Caption = 'GEKAP Dataset';
    UsageCategory = ReportsAndAnalysis;
    //DefaultLayout = RDLC;
    //RDLCLayout = './ItemLedgerEntryReportIPK.rdlc';


    dataset
    {

        dataitem("Item Ledger Entry"; "Item Ledger Entry")
        {

            DataItemTableView = sorting("Entry No.") where("Entry Type" = filter(Purchase | Sale));
            RequestFilterFields = "Item No.", "Posting Date", "Entry Type", "Source No.";
            column(Item_No_; "Item No.") { }
            column(Description; ItemDescription) { }
            column(Item_Category_Code; ItemCategoryCode) { }
            column(Posting_Date; "Posting Date") { }
            column(Entry_Type; "Entry Type") { }
            column(ShipmentReceiptExternalDocNo; ShipmentReceiptExternalDocNo) { }
            column(InvoiceExternalDocNo; InvoiceExternalDocNo) { }
            column(Source_No_; "Source No.") { }
            column(Source_Name; SourceName) { }
            column(Business_Posting_Group; BusinessPostingGroup) { }
            column(Currency_Code; CurrencyCode) { }
            column(Quantity; Quantity) { }
            column(UnitQuantity; UnitQuantity) { }

            // Report header fields
            // column(CompanyName; CompanyInformation.Name) { }
            // column(ReportTitle; 'Item Ledger Entry Report') { }
            // column("UserID"; UserId()) { }
            // column(DateTimeStamp; Format(CurrentDateTime())) { }

            trigger OnAfterGetRecord()
            var
                Customer: Record Customer;
                ItemRec: Record Item;
                PurchInvHeader: Record "Purch. Inv. Header";
                PurchRcptHeader: Record "Purch. Rcpt. Header";
                SalesInvoiceHeader: Record "Sales Invoice Header";
                SalesShipmentHeader: Record "Sales Shipment Header";
                ValueEntry: Record "Value Entry";
                Vendor: Record Vendor;
            begin
                // Get Item information
                Clear(ItemDescription);
                Clear(ItemCategoryCode);
                Clear(UnitPerParcel);
                Clear(UnitQuantity);

                if ItemRec.Get("Item No.") then begin
                    ItemDescription := ItemRec.Description;
                    ItemCategoryCode := ItemRec."Item Category Code";
                    UnitPerParcel := ItemRec."Units per Parcel";

                    // Calculate Unit Quantity
                    if UnitPerParcel <> 0 then
                        UnitQuantity := Quantity * UnitPerParcel
                    else
                        UnitQuantity := Quantity;
                end;

                // Initialize variables
                Clear(ShipmentReceiptExternalDocNo);
                Clear(InvoiceExternalDocNo);
                Clear(CurrencyCode);
                Clear(SourceName);
                Clear(BusinessPostingGroup);

                // Get Shipment/Receipt external document numbers
                case "Document Type" of
                    "Document Type"::"Sales Shipment":
                        if SalesShipmentHeader.Get("Document No.") then
                            ShipmentReceiptExternalDocNo := SalesShipmentHeader."External Document No.";
                    "Document Type"::"Purchase Receipt":
                        if PurchRcptHeader.Get("Document No.") then
                            ShipmentReceiptExternalDocNo := PurchRcptHeader."Vendor Shipment No.";
                end;

                // Get related invoice information using Value Entries
                ValueEntry.SetCurrentKey("Item Ledger Entry No.", "Entry Type");
                ValueEntry.SetRange("Item Ledger Entry No.", "Entry No.");
                ValueEntry.SetRange("Entry Type", ValueEntry."Entry Type"::"Direct Cost");
                ValueEntry.SetFilter("Invoiced Quantity", '<>0');
                if ValueEntry.FindSet() then
                    repeat
                        case ValueEntry."Document Type" of
                            ValueEntry."Document Type"::"Sales Invoice":
                                if InvoiceExternalDocNo = '' then
                                    if SalesInvoiceHeader.Get(ValueEntry."Document No.") then begin
                                        InvoiceExternalDocNo := SalesInvoiceHeader."External Document No.";
                                        if "Source Type" = "Source Type"::Customer then
                                            CurrencyCode := SalesInvoiceHeader."Currency Code";
                                    end;
                            ValueEntry."Document Type"::"Purchase Invoice":
                                if InvoiceExternalDocNo = '' then
                                    if PurchInvHeader.Get(ValueEntry."Document No.") then begin
                                        InvoiceExternalDocNo := PurchInvHeader."Vendor Invoice No.";
                                        if "Source Type" = "Source Type"::Vendor then
                                            CurrencyCode := PurchInvHeader."Currency Code";
                                    end;
                        end;
                    until (ValueEntry.Next() = 0) or (InvoiceExternalDocNo <> '');

                // Get source name and business posting group
                case "Source Type" of
                    "Source Type"::Customer:
                        if Customer.Get("Source No.") then begin
                            SourceName := Customer.Name;
                            BusinessPostingGroup := Customer."Customer Posting Group";
                        end;
                    "Source Type"::Vendor:
                        if Vendor.Get("Source No.") then begin
                            SourceName := Vendor.Name;
                            BusinessPostingGroup := Vendor."Vendor Posting Group";
                        end;
                end;
            end;
        }
    }

    trigger OnPreReport()
    begin
        CompanyInformation.Get();
    end;

    var
        CompanyInformation: Record "Company Information";
        CurrencyCode: Code[10];
        BusinessPostingGroup: Code[20];
        ItemCategoryCode: Code[20];
        UnitPerParcel: Decimal;
        UnitQuantity: Decimal;
        InvoiceExternalDocNo: Text[35];
        ShipmentReceiptExternalDocNo: Text[35];
        ItemDescription: Text[100];
        SourceName: Text[100];
}