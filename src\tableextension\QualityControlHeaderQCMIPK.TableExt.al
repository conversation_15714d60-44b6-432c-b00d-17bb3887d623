tableextension 60018 "Quality Control Header QCM IPK" extends "Quality Control Header QCM"
{
    fields
    {

        field(60000; "Source Document No. IPK"; Code[20])
        {
            Caption = 'Source Document No.';
            ToolTip = 'Specifies the value of the Source Document No. field.';
        }
        field(60001; "Source Document Line No. IPK"; Integer)
        {
            Caption = 'Source Document Line No.';
            ToolTip = 'Specifies the value of the Source Document Line No. field.';
        }
        field(60002; "Source Document Type IPK"; Enum "QC Source Document Type IPK")
        {
            Caption = 'Source Document Type';
            ToolTip = 'Specifies the value of the Source Document Type field.';
            AllowInCustomizations = Always;
        }
        field(60003; "Item Unit Per Parcel IPK"; Decimal)
        {
            Caption = 'Item Unit Per Parcel';
            Editable = false;
            ToolTip = 'Specifies the value of the Item Unit Per Parcel field.';
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Units per Parcel" where("No." = field("Item No.")));
            AllowInCustomizations = Always;
        }
        field(60004; "External Document No. IPK"; Code[35])
        {
            Caption = 'External Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the External Document No. field.';
            AllowInCustomizations = Always;
        }
        field(60005; "Quantity IPK"; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
            DecimalPlaces = 0 : 5;
            Editable = false;
        }
        field(60006; "Source Document Vend. Name IPK"; Text[100])
        {
            Caption = 'Source Document Vendor Name';
            ToolTip = 'Specifies the value of the Source Document Vendor Name field.';
            Editable = false;
        }
        field(60007; "Score IPK"; Decimal)
        {
            DataClassification = ToBeClassified;
            Caption = 'Score';
            ToolTip = 'Specifies the value of the Score field.';
        }
        modify(Status)
        {
            trigger OnAfterValidate()
            var
                IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
                ItemCategory: Record "Item Category";
                PackageNoInfoLine: Record "Package No. Info. Line IPK";
                // PackageNoInformation: Record "Package No. Information";
                PackageTransferHeader: Record "Package Transfer Header IPK";
                QualityControlMngtSetupQCM: Record "Quality Control Mngt Setup QCM";
                ConfirmManagement: Codeunit "Confirm Management";
                IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
                IpekEvents: Codeunit "Ipek Events IPK";
                IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";
                IpekQcManagement: Codeunit "Ipek Quality Management IPK";
                InvalidItemsCategoryLbl: Label 'Something wrong with selected items category.';
            begin
                if ((xRec.Status <> Rec.Status) and (xRec.Status = xRec.Status::"Input Pending") and not (Rec.Type = Rec.Type::Production)) then begin
                    IpekPamukSetup.GetRecordOnce();//alper
                    if ConfirmManagement.GetResponse('An e-mail will be sent to authorized users. Do you confirm?') then
                        IpekEvents.QualityControlStatusChangedBe(IpekBasicFunctions.GetUserNameFromSecurityId(Rec.SystemModifiedBy), Rec."Item No.", Rec."Lot No.", Rec."Source Document No. IPK", Rec."No.", IpekQcManagement.GetMailListByQCStatus(Rec.Status), Rec.Status/*IpekPamukSetup."Quality Control Mail Group"*/);


                end
                else begin
                    if not ConfirmManagement.GetResponse('Do you want to change this packages location ?') then
                        exit;
                    QualityControlMngtSetupQCM.GetRecordOnce();
                    if not ItemCategory.Get(Rec."Item Category") then
                        Error(InvalidItemsCategoryLbl);

                    // if ((xRec.Status = xRec.Status::"Input Pending") and ((Rec.Status = Rec.Status::Acceptance) or (Rec.Status = Rec.Status::"Conditional Acceptance"))) then
                    //     exit;

                    PackageNoInfoLine.SetRange("Lot No.", Rec."Lot No.");
                    if not (Rec.Type = Rec.Type::Production) then
                        case xRec.Status of
                            Rec.Status::Rejection:
                                PackageNoInfoLine.SetRange("Location Code", QualityControlMngtSetupQCM."Rejection Location IPK");
                            Rec.Status::Quarantine:
                                PackageNoInfoLine.SetRange("Location Code", QualityControlMngtSetupQCM."Quarantine Location IPK");
                            Rec.Status::Acceptance, Rec.Status::"Conditional Acceptance":
                                PackageNoInfoLine.SetRange("Location Code", ItemCategory."Default Receive Location IPK");
                        end
                    else
                        PackageNoInfoLine.SetRange("Package No.", Rec."Package No.");

                    if (PackageNoInfoLine.FindSet()) and not ((Rec.Type = Rec.Type::Production) and (xRec.Status = xRec.Status::"Input Pending") and ((Rec.Status = Rec.Status::Acceptance) or (Rec.Status = Rec.Status::"Conditional Acceptance"))) then begin

                        PackageTransferHeader.Init();
                        PackageTransferHeader.Insert(true);
                        // PackageTransferHeader.Validate("Transfer-to Code", LocationCode);
                        PackageTransferHeader.Validate("Source Document No.", Rec."No.");

                        case Rec.Status of
                            Rec.Status::Rejection:
                                PackageTransferHeader.Validate("Transfer-to Code", QualityControlMngtSetupQCM."Rejection Location IPK");
                            Rec.Status::Quarantine:
                                PackageTransferHeader.Validate("Transfer-to Code", QualityControlMngtSetupQCM."Quarantine Location IPK");
                            Rec.Status::Acceptance, Rec.Status::"Conditional Acceptance":
                                PackageTransferHeader.Validate("Transfer-to Code", ItemCategory."Default Receive Location IPK");

                        end;

                        PackageTransferHeader.Modify(true);
                        Rec.Modify(true);
                        repeat
                            PackageTransferHeader.Validate(Barcode, PackageNoInfoLine."Package No.");//Aynı Lot, Aynı paket no çıkma ihtimali olmamalı
                            PackageTransferHeader.Validate("Package Transfer Type", PackageTransferHeader."Package Transfer Type"::"Quality Control");
                        until PackageNoInfoLine.Next() = 0;

                        IpekPackageTransMgt.ShipAndRecievePackagetransferHeader(PackageTransferHeader, Rec)
                    end;


                end;

            end;
        }

    }
    keys
    {
        key(Key1; "Package No.", Type, Status)
        {
        }
    }
}