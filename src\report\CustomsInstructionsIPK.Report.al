report 60004 "Customs Instructions IPK"
{
    ApplicationArea = All;
    Caption = 'Customs Instructions';
    UsageCategory = ReportsAndAnalysis;
    DefaultLayout = Excel;
    ExcelLayout = 'test2.xlsx';
    //Gümrükleme Talimatı
    //Nakliyeci tel no yok eklenecek mi diye sor
    //cfr konusunu denize sor
    // unit price alanlarını denize sor
    dataset
    {
        dataitem("Load Header IPK"; "Load Header IPK")
        {
            column(No_; "No.")
            {
            }
            column(Load_Place; "Load Location")
            {
            }
            column(Truck_License_Plate; "Truck License Plate")
            {
            }
            column(Trailer_License_Plate; "Trailer License Plate")
            {
            }
            dataitem("Domestic_Shipping Agent"; "Shipping Agent")
            {
                DataItemLink = Code = field("Domestic Shipping Agent Code");

                column(Domestic_Shipping_Agent_Name; Name)
                {
                }
            }
            column(Posting_Date; "Posting Date")
            {
            }
            dataitem("Load Planning Line IPK"; "Load Planning Line IPK")
            {
                DataItemLink = "Warehouse Shipment No." = field("Warehouse Shipment No."), "Document No." = field("No.");

                column(Quantity_Planned; "Quantity Planned")
                {
                }
                column(Line_Net_Weight; "Line Net Weight")
                {
                }
                column(Line_Gross_Weight; "Line Gross Weight")
                {
                }
                column(Item_No_; "Item No.")
                {
                }
                column(Item_Description; Description)
                {
                }
                column(Sale_Order_No; "Source No.")
                {
                }

                dataitem("Sales Header"; "Sales Header")
                {
                    DataItemLink = "No." = field("Source No.");

                    dataitem("Company Bank Account"; "Bank Account")
                    {
                        DataItemLink = "No." = field("Company Bank Account Code");
                        column(Bank_Name; Name)
                        {
                        }
                    }
                    dataitem("Payment Term Translation"; "Payment Term Translation")
                    {
                        DataItemLink = "Payment Term" = field("Payment Terms Code"), "Language Code" = field("Language Code");
                        column(Payment_Terms_Description; Description)
                        {
                        }
                        column(Payment_Terms_Code; "Payment Term")
                        {
                        }
                    }
                }
                dataitem("Sales Line"; "Sales Line")
                {
                    DataItemLink = "No." = field("Source No."), "Line No." = field("Source Line No.");

                    column(Line_Unit_Quantity_IPK; "Line Unit Quantity IPK")
                    {
                    }
                    column(Unit_Price; "Unit Price")
                    {
                    }
                    column(Unit_Cost; "Unit Cost")
                    {
                    }
                }
            }
            dataitem("Warehouse Shipment Header"; "Warehouse Shipment Header")
            {
                DataItemLink = "No." = field("Warehouse Shipment No.");
                column(External_Document_No_; "External Document No.")
                {
                }
                column(CustomsIPK_WarehouseShipmentHeader; "Customs Code IPK")
                {
                }
                column(ShipmentMethodCode_ShipToCustomer; "Shipment Method Code")
                {
                }
                column(Vessel_Name_IPK; "Vessel Name IPK")
                {
                }
                column(VoyageNoIPK_WarehouseShipmentHeader; "Voyage No. IPK")
                {
                }
                column(PoA_IPK; "PoA Code IPK")
                {
                }
                column(PoL_IPK; "PoL Code IPK")
                {
                }
                column(FlagIPK_WarehouseShipmentHeader; "Flag IPK")
                {
                }
                column(CuttoffDateIPK_WarehouseShipmentHeader; "Cutt-off Date IPK")
                {
                }
                column(Shipping_Agent_Code; "Shipping Agent Code")
                {
                }
                column(Shipping_Agent_Service_Code; "Shipping Agent Service Code")
                {
                }
                column(Customs_Officer_IPK; "Customs Officer IPK")
                {
                }
                dataitem("Shipping Agent"; "Shipping Agent")
                {
                    DataItemLink = Code = field("Shipping Agent Code");
                    dataitem(Vendor; Vendor)
                    {
                        DataItemLink = "No." = field("Vendor No. INF");
                        column(Phone_No_; "Phone No.")
                        {
                        }
                        column(Name; Name)
                        {
                        }


                    }
                }

                dataitem("Ship-to Address"; "Ship-to Address")
                {
                    DataItemLink = Code = field("Ship-to Code INF");
                    column(Name_ShiptoAddress; Name)
                    {
                    }
                    column(Address_ShipToCustomer; Address)
                    {
                    }
                    column(Address2_ShipToCustomer; "Address 2")
                    {
                    }
                    column(City_ShipToCustomer; City)
                    {
                    }
                    column(County_ShipToCustomer; County)
                    {
                    }
                }
                dataitem("Sell-to Customer"; Customer)
                {
                    DataItemLink = "No." = field("Source No. INF");
                    column(Name_SelltoCustomer; Name)
                    {
                    }
                    column(Address_SelltoCustomer; Address)
                    {
                    }
                    column(Address2_SelltoCustomer; "Address 2")
                    {
                    }
                    column(City_SelltoCustomer; City)
                    {
                    }
                    column(County_SelltoCustomer; County)
                    {
                    }
                }
            }

        }
    }
}