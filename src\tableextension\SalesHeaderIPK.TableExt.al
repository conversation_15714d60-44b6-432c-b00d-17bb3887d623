tableextension 60010 "Sales Header IPK" extends "Sales Header"
{
    fields
    {
        field(60000; "Total Net Weight IPK"; Decimal)
        {
            Caption = 'Total Net Weight';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Sales Line"."Line Net Weight IPK" where("Document Type" = field("Document Type"), "Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Net Weight field.';
        }
        field(60001; "Total Gross Weight IPK"; Decimal)
        {
            Caption = 'Total Gross Weight';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Sales Line"."Line Gross Weight IPK" where("Document Type" = field("Document Type"), "Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Gross Weight field.';
        }
        field(60002; "Total Volume IPK"; Decimal)
        {
            Caption = 'Total Volume';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Sales Line"."Line Volume IPK" where("Document Type" = field("Document Type"), "Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Volume field.';
        }
        field(60003; "Export No. IPK"; Integer)
        {
            Caption = 'Export No.';
            ToolTip = 'Specifies the value of the Export No. field.';
        }
        field(60004; "Freight Amount IPK"; Decimal)
        {
            Caption = 'Freight Amount';
            ToolTip = 'Specifies the value of the Freight Amount field.';
        }
        field(60005; "Total Pack IPK"; Decimal)
        {
            AllowInCustomizations = Always;
            Caption = 'Total Pack';
            ToolTip = 'Specifies the value of the Total Pack field.';
            FieldClass = FlowField;
            CalcFormula = sum("Sales Line"."Quantity" where("Document Type" = field("Document Type"), "Document No." = field("No."), Type = const(Item)));
            Editable = false;
        }
        // field(60006; "Requested Delivery Date IPK"; Date)
        // {
        //     Caption = 'Requested Delivery Date IPK';
        //     ToolTip = 'Specifies the value of the Requested Delivery Date IPK field.';
        // }
    }
}