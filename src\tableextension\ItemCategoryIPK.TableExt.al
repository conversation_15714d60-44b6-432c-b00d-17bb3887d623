tableextension 60017 "Item Category IPK" extends "Item Category"
{
    fields
    {
        field(60000; "Default Receive Location IPK"; Code[10])
        {
            Caption = 'Default Receive Location';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Default Receive Location field.';
        }
        field(60001; "Default Consumption Loc. IPK"; Code[10])
        {
            Caption = 'Default Consumption Location';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Default Consumption Location field.';
        }
        field(60002; "Waterjet IPK"; Boolean)
        {
            Caption = 'Waterjet';
            ToolTip = 'Specifies the value of the Waterjet field.';
        }
        field(60003; "Organic IPK"; Enum "Organic IPK")
        {
            Caption = 'Organic';
            ToolTip = 'Specifies the value of the Organic field.';
        }
    }
    trigger OnModify()
    begin
        Rec.TestField("Organic IPK");
    end;
}