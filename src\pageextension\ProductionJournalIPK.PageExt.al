pageextension 60072 "Production Journal IPK" extends "Production Journal"
{
    layout
    {

        addafter(Quantity)
        {
            field("Total Qty. on Location IPK"; Rec."Total Qty. on Location IPK")
            {
                ApplicationArea = All;
            }
            field("Physical Quantity IPK"; Rec."Physical Quantity IPK")
            {
                ApplicationArea = All;
            }
        }



        modify("Variant Code")
        {
            Visible = true;
        }
        moveafter("Item No."; "Variant Code")
        modify("Location Code")
        {
            Visible = true;
        }

        modify("Setup Time")
        {
            Visible = false;
        }
        modify("Run Time")
        {
            Visible = false;
        }
        modify("Scrap Qty.")
        {
            Visible = false;
        }
        modify(Finished)
        {
            Visible = false;
        }
    }
}