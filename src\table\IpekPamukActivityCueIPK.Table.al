table 60012 "Ipek Pamuk Activity Cue IPK"
{
    Caption = 'Ipek Pamuk Activity Cue';
    DataClassification = CustomerContent;

    fields
    {
        field(1; PK; Code[250])
        {
            Caption = 'PK';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Planned Production Orders"; Integer)
        {
            Caption = 'Planned Production Orders';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Production Order" where(Status = const("Firm Planned"), Status = const(Planned)));
            ToolTip = 'Specifies the value of the Planned Production Orders field.';
        }
        field(3; "Released Production Orders"; Integer)
        {
            Caption = 'Released Production Orders';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Production Order" where(Status = const(Released),
            "Consumption Location Code IPK" = field("Waiting Pac. Tran. Loc. Filt.")));
            ToolTip = 'Specifies the value of the Released Production Orders field.';
        }
        field(4; "Production QCD"; Integer)
        {
            Caption = 'Production';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Quality Control Header QCM" where(Status = filter("Input Pending" | " "), Type = const(Production)));
            ToolTip = 'Specifies the value of the Production Quality Control Documents field.';
        }
        field(5; "Purchase QCD"; Integer)
        {
            Caption = 'Purchase';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Quality Control Header QCM" where(Status = filter("Input Pending" | " "), Type = const(Purchase)));
            ToolTip = 'Specifies the value of the Purchased Quality Control Documents field.';
        }
        field(6; "Processed Warehouse Recipt"; Integer)
        {
            Caption = 'Processed Warehouse Recipt';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Warehouse Receipt Header" where("Quality Control Not Proc. IPK" = const(false)));
            ToolTip = 'Specifies the value of the Processed Warehouse Recipt.';

        }
        field(7; "Waiting Package Transfers"; Integer)
        {
            Caption = 'Waiting Package Transfers';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Transfer Header IPK" where(Shipped = const(true), Received = const(false),
            "Transfer-to Code" = field("Waiting Pac. Tran. Loc. Filt.")));
            ToolTip = 'Specifies the value of the Waiting Package Transfers.';

        }
        field(8; "Waiting Pac. Tran. Loc. Filt."; Text[100])
        {
            Caption = 'Waiting Package Transfers Location Filter';
            Editable = false;
            FieldClass = FlowFilter;
            ToolTip = 'Specifies the value of the Waiting Package Transfers Location Filter.';
        }
        field(9; "User ID Filter"; Code[50])
        {
            Caption = 'User ID Filter';
            FieldClass = FlowFilter;
        }
        field(10; "Ready-to Load"; Integer)
        {
            Caption = 'Ready-to Load';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Load Header IPK" where(Status = filter("Ready-to Load" | Loading)));
            ToolTip = 'Specifies the value of the Ready-to Load field.';
        }
        field(11; "Ready-to Ship"; Integer)
        {
            Caption = 'Ready-to Ship';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Load Header IPK" where(Status = const(Loaded)));
            ToolTip = 'Specifies the value of the Ready-to Ship field.';
        }
        field(12; "Ready-to Invoice"; Integer)
        {
            Caption = 'Ready-to Invoice';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Sales Header" where("Shipped Not Invoiced" = const(true)));
            ToolTip = 'Specifies the value of the Ready-to Invoice field.';
            AllowInCustomizations = Always;
        }
        field(13; "Rdy. To Post Package Transfers"; Integer)
        {
            Caption = 'Waiting Package Transfers';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Transfer Header IPK" where(Shipped = const(false), Received = const(false),
            "Transfer-from Code" = const('MALKABUL')));//alper
            ToolTip = 'Specifies the value of the Waiting Package Transfers.';

        }
        field(14; "Ready-to Combine"; Integer)
        {
            Caption = 'Ready-to Combine';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Combine Header IPK" where(Shipped = const(true), Received = const(false)));
            ToolTip = 'Specifies the value of the Ready-to Combine field.';
        }
        field(15; "Pending Bom Approvals"; Integer)
        {
            Caption = 'Pending Bom Approvals';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Production BOM Header" where(Status = const("Under Development"),
            "Approval Status IPK" = const(Open)));
            ToolTip = 'Specifies the value of the Pending Bom Approvals field.';
        }
    }

    keys
    {
        key(PK; PK)
        {
            Clustered = true;
        }
    }
}