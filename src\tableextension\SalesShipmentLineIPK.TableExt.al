tableextension 60002 "Sales Shipment Line IPK" extends "Sales Shipment Line"
{
    fields
    {
        field(60000; "Warehouse Shipment No. IPK"; Code[20])
        {
            Caption = 'Warehouse Shipment No.';
            ToolTip = 'Specifies the warehouse shipment number.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Posted Whse. Shipment Line"."Whse. Shipment No." where("Posted Source No." = field("Document No.")));
        }
        field(60005; "FOB Piece Unit Price IPK"; Decimal)
        {
            AllowInCustomizations = Always;
            Caption = 'FOB Piece Unit Price';
            ToolTip = 'Specifies the value of the FOB Piece Unit Price field.';
            AutoFormatExpression = Rec."Currency Code";
            AutoFormatType = 2;
            trigger OnValidate()
            begin
                Rec.Validate("FOB Line Amount IPK", "Freight Inc PieceUnitPrice IPK" * Rec."Units per Parcel" * Rec.Quantity);
            end;
        }
        field(60006; "Freight Inc PieceUnitPrice IPK"; Decimal)
        {
            AllowInCustomizations = Always;
            Caption = 'CFR Piece Unit Price';
            ToolTip = 'Specifies the value of the CFR Piece Unit Price field.';
            AutoFormatExpression = Rec."Currency Code";
            AutoFormatType = 2;
            Editable = false;

            trigger OnValidate()
            begin
                Rec.Validate("Q/O Unit Price INF", Rec."FOB Piece Unit Price IPK" * Rec."Units per Parcel");
            end;
        }
        field(60007; "FOB Line Amount IPK"; Decimal)
        {
            AllowInCustomizations = Always;
            Caption = 'FOB Line Amount';
            ToolTip = 'Specifies the value of the FOB Line Amount field.';
            AutoFormatExpression = Rec."Currency Code";
            AutoFormatType = 2;
            Editable = false;
            DecimalPlaces = 0 : 2;
        }
    }
}