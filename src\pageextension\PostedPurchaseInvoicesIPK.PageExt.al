pageextension 60073 "Posted Purchase Invoices IPK" extends "Posted Purchase Invoices"
{
    layout
    {
        addafter("Amount Including VAT")
        {
            field("Amount Including VAT(LCY) IPK"; LcyAmount)
            {
                Visible = true;
                ApplicationArea = All;
                Caption = 'Amount Including VAT(LCY)';
                ToolTip = 'Specifies the value of the Amount Including VAT(LCY) field.';

            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        if Rec."Currency Factor" > 0 then
            LcyAmount := Rec."Amount Including VAT" * (1 / Rec."Currency Factor")
        else
            LcyAmount := Rec."Amount Including VAT";
    end;

    var
        LcyAmount: Decimal;
}