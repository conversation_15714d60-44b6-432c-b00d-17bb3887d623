page 60005 "Load Planning Subpage IPK"
{
    ApplicationArea = All;
    Caption = 'Load Planning Subpage';
    PageType = ListPart;
    SourceTable = "Load Planning Line IPK";
    AutoSplitKey = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Warehouse Shipment Line No."; Rec."Warehouse Shipment Line No.")
                {
                    QuickEntry = false;
                }
                field("Loading Order"; Rec."Loading Order")
                {
                }
                field("Source No."; Rec."Source No.")
                {
                    Editable = false;
                }
                field("Source Line No."; Rec."Source Line No.")
                {
                    Editable = false;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field(Description; Rec.Description)
                {
                    Editable = false;
                }
                // field("FOB Unit Price"; Rec."FOB Unit Price")
                // {
                // }
                // field(CFRUnitPrice; IpekSalesManagement.CalculateCFRUnitPriceFromLoadPlanningLine(Rec))
                // {
                //     Caption = 'CFR Unit Price';
                //     ToolTip = 'Specifies the CFR unit price of the item.';
                //     Editable = false;
                // }
                field("Line Unit Quantity"; Rec."Line Unit Quantity" / 1)
                {
                    Caption = 'Line Unit Quantity';
                    ToolTip = 'Specifies the value of the Line Unit Quantity field.';
                    DecimalPlaces = 0 : 2;
                }
                field("Line Unit Qty. Loaded"; Rec."Quantity Loaded" * Rec."Units per Parcel")
                {
                    Caption = 'Line Unit Quantity Loaded';
                    ToolTip = 'Specifies the value of the Line Unit Quantity Loaded field.';
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                }
                field("Quantity to Load"; Rec."Quantity to Load")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();

                    end;
                }

                field("Quantity Planned"; Rec."Quantity Planned")
                {
                    //StyleExpr = StyleTxt;
                }
                field("Quantity Loaded"; Rec."Quantity Loaded")
                {
                    Editable = false;
                }
                field(RemainingQuantity; Rec."Quantity to Load" - Rec."Quantity Loaded")
                {
                    Caption = 'Remaining Quantity';
                    ToolTip = 'Specifies the remaining quantity to load.';
                    Editable = false;
                }
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        StyleTxt := SetStyle();
    end;

    procedure SetStyle() Style: PageStyle
    begin
        if Rec."Quantity Planned" > Rec.Quantity then
            exit(PageStyle::Attention);
    end;

    var
        //IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
        StyleTxt: PageStyle;
}