<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <DataSources>
    <DataSource Name="DataSource1">
      <ConnectionProperties>
        <DataProvider>None</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:DataSourceID>c1e1b1e2-1234-5678-9abc-def012345678</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>1cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PackageNo">
                      <Value>=Fields!PackageNo.Value</Value>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="LocationCodeMXW">
                      <Value>=Fields!LocationCodeMXW.Value</Value>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PackageNoBarCode">
                      <Value>=Fields!PackageNoBarCode.Value</Value>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PackageNoQRCode">
                      <Value>=Fields!PackageNoQRCode.Value</Value>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CreatedAtMXW_PackageNoInformation">
                      <Value>=Fields!CreatedAtMXW_PackageNoInformation.Value</Value>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DocumentNoMXW_PackageNoInformation">
                      <Value>=Fields!DocumentNoMXW_PackageNoInformation.Value</Value>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ProducedByMXW_PackageNoInformation">
                      <Value>=Fields!ProducedByMXW_PackageNoInformation.Value</Value>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>PackageNoInformation</DataSetName>
      </Tablix>
    </ReportItems>
    <Height>2cm</Height>
  </Body>
  <Width>16cm</Width>
  <Page>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>2cm</LeftMargin>
    <RightMargin>2cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
  </Page>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="PackageNo">
          <DataField>PackageNo</DataField>
        </Field>
        <Field Name="LocationCodeMXW">
          <DataField>LocationCodeMXW</DataField>
        </Field>
        <Field Name="PackageNoBarCode">
          <DataField>PackageNoBarCode</DataField>
        </Field>
        <Field Name="PackageNoQRCode">
          <DataField>PackageNoQRCode</DataField>
        </Field>
        <Field Name="CreatedAtMXW_PackageNoInformation">
          <DataField>CreatedAtMXW_PackageNoInformation</DataField>
        </Field>
        <Field Name="DocumentNoMXW_PackageNoInformation">
          <DataField>DocumentNoMXW_PackageNoInformation</DataField>
        </Field>
        <Field Name="ProducedByMXW_PackageNoInformation">
          <DataField>ProducedByMXW_PackageNoInformation</DataField>
        </Field>
        <Field Name="Description_WarehouseReceiptLineDtlMXW">
          <DataField>Description_WarehouseReceiptLineDtlMXW</DataField>
        </Field>
        <Field Name="LineItemNo_WarehouseReceiptLineDtlMXW">
          <DataField>LineItemNo_WarehouseReceiptLineDtlMXW</DataField>
        </Field>
        <Field Name="LineVariantCode_WarehouseReceiptLineDtlMXW">
          <DataField>LineVariantCode_WarehouseReceiptLineDtlMXW</DataField>
        </Field>
        <Field Name="LotNo_WarehouseReceiptLineDtlMXW">
          <DataField>LotNo_WarehouseReceiptLineDtlMXW</DataField>
        </Field>
        <Field Name="Quantity_WarehouseReceiptLineDtlMXW">
          <DataField>Quantity_WarehouseReceiptLineDtlMXW</DataField>
        </Field>
        <Field Name="Quantity_WarehouseReceiptLineDtlMXWFormat">
          <DataField>Quantity_WarehouseReceiptLineDtlMXWFormat</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>