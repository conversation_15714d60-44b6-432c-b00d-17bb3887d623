tableextension 60009 "Warehouse Request IPK" extends "Warehouse Request"
{
    fields
    {
        field(60000; "Available Qty.-to Receive IPK"; Decimal)
        {
            Caption = 'Available Quantity-to Receive';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Purchase Line"."Whse. Rcpt. Qty-to Receive IPK" where("Document No." = field("Source No.")));
            ToolTip = 'Specifies the value of the Available Quantity-to Receive field.';
        }
        field(60001; "Destination Name IPK"; Text[100])
        {
            Caption = 'Destination Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Destination No.")));
            ToolTip = 'Specifies the value of the Destination Name field.';
        }
    }
}