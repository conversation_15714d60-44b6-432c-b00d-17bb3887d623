tableextension 60021 "Prod. Order Component IPK" extends "Prod. Order Component"
{
    fields
    {
        field(60000; "Quantity At Location IPK"; Decimal)
        {
            Caption = 'Quantity At Location';
            ToolTip = 'Specifies the value of the Quantity At Location field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Item No." = field("Item No."),
                                                                 "Variant Code" = field("Variant Code"),
                                                                 "Location Code" = field("Location Code"),
                                                                 Open = const(true)));
        }
        field(60001; "Category Code IPK"; Code[20])
        {
            Caption = 'Category Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item"."Item Category Code" where("No." = field("Item No.")));
            ToolTip = 'Specifies the value of the MyField field.';
        }
        field(60002; "Category Description IPK"; Text[100])
        {
            Caption = 'Category Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Category".Description where(Code = field("Category Code IPK")));
            ToolTip = 'Specifies the value of the MyField field.';
        }
        field(60003; "Quantity At All Locations IPK"; Decimal)
        {
            Caption = 'Quantity At All Locations';
            ToolTip = 'Specifies the value of the Quantity At All Locations field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Item No." = field("Item No."),
                                                                 "Variant Code" = field("Variant Code"),
                                                                 Open = const(true)));
        }
        field(60004; "Consumed Quantity IPK"; Decimal)
        {
            Caption = 'Consumed Quantity';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Order Type" = const(Production), "Order No." = field("Prod. Order No."), "Item No." = field("Item No."), "Variant Code" = field("Variant Code")));
            ToolTip = 'Specifies the value of the Consumed Quantity field.';
        }
        field(60005; "Additional Cons. Qty. IPK"; Decimal)
        {
            Caption = 'Additional Consumption Quantity';
            ToolTip = 'Specifies the value of the Additional Consumption Quantity field.';
        }

    }
}