tableextension 60024 "Production BOM Line IPK" extends "Production BOM Line"
{
    fields
    {
        field(60000; "Item Category Code IPK"; Code[20])
        {
            Caption = 'Item Category Code';
            ToolTip = 'Specifies the value of the Item Category Code field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Item Category Code" where("No." = field("No.")));
        }
        field(60001; "Item Category Description IPK"; Text[100])
        {
            Caption = 'Item Category Description';
            ToolTip = 'Specifies the value of the Item Category Description field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Category".Description where(Code = field("Item Category Code IPK")));
        }
    }
    // trigger OnBeforeModify()
    // var
    //     ProductionBOMHeader: Record "Production BOM Header";
    // begin
    //     ProductionBOMHeader.Get(Rec."Production BOM No.");
    //     ProductionBOMHeader.Validate(Status, ProductionBOMHeader.Status::"Under Development");
    //     ProductionBOMHeader.Validate("Approval Status IPK", ProductionBOMHeader."Approval Status IPK"::Open);
    //     ProductionBOMHeader.Modify(true);
    // end;

    // trigger OnBeforeInsert()
    // var
    //     ProductionBOMHeader: Record "Production BOM Header";
    // begin
    //     ProductionBOMHeader.Get(Rec."Production BOM No.");
    //     ProductionBOMHeader.Validate(Status, ProductionBOMHeader.Status::"Under Development");
    //     ProductionBOMHeader.Validate("Approval Status IPK", ProductionBOMHeader."Approval Status IPK"::Open);
    //     ProductionBOMHeader.Modify(true);
    // end;
}