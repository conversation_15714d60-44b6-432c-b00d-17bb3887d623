page 60002 "Load Line Subpage IPK"
{
    ApplicationArea = All;
    Caption = 'Load Line Subpage';
    PageType = ListPart;
    SourceTable = "Load Line IPK";
    Extensible = true;
    InsertAllowed = false;
    ModifyAllowed = false;
    SourceTableView = sorting(SystemCreatedAt) order(descending);
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Label No.")
                {
                    ToolTip = 'Specifies the value of the Label No. field.';
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
            }
        }
    }
}