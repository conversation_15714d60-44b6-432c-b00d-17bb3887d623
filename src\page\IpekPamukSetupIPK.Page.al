page 60007 "Ipek Pamuk Setup IPK"
{

    PageType = Card;
    SourceTable = "Ipek Pamuk Setup IPK";
    Caption = 'Ipek Pamuk Setup';
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = Administration;
    ApplicationArea = All;



    layout
    {
        area(Content)
        {
            group(Sales)
            {
                Caption = 'Sales Setup';

                field("Load No. Series"; Rec."Load No. Series")
                {
                }
                field("Certificate No. Series"; Rec."Certificate No. Series")
                {
                }
                field("Freight Item Charge No."; Rec."Freight Item Charge No.")
                {
                }
            }
            group(Production)
            {
                Caption = 'Production Setup';

                field("Shift Start Time"; Rec."Shift Start Time")
                {
                }
                field("Consumption Jnl. Template Name"; Rec."Consumption Jnl. Template Name")
                {
                }
                field("Consumption Jnl. Batch Name"; Rec."Consumption Jnl. Batch Name")
                {
                }
                field("Output Journal Template Name"; Rec."Output Journal Template Name")
                {
                }
                field("Output Journal Batch Name"; Rec."Output Journal Batch Name")
                {
                }
                field("Cutting Item No."; Rec."Cutting Item No.")
                {
                }
                field("Organic Cutting Item No."; Rec."Organic Cutting Item No.")
                {
                }
                field("Default Cutting Location"; Rec."Default Cutting Location")
                {
                }
                field("Waterjet Item Category Filter"; Rec."Waterjet Item Category Filter")
                {
                }

            }
            group(Transfer)
            {
                Caption = 'Transfer Setup';
                field("Package Transfer No. Series"; Rec."Package Transfer No. Series")
                {
                }
                field("Package Combine No. Series"; Rec."Package Combine No. Series")
                {
                }
                field("Package Tran. Jnl. Temp. Name"; Rec."Package Tran. Jnl. Temp. Name")
                {
                }
                field("Package Tran. Jnl. Batch Name"; Rec."Package Tran. Jnl. Batch Name")
                {
                }
            }
            group(MailGroups)
            {
                Caption = 'Mail Groups';

                field("Load Card Mail Group"; Rec."Load Card Mail Group")
                {
                }
                field("Quality Control Mail Group"; Rec."Quality Control Mail Group")
                {
                }
                field("Warehouse Recipt Mail Group"; Rec."Warehouse Recipt Mail Group")
                {
                }
                field("Warehouse Recipt Created Mail Group"; Rec."Warehouse Recipt Created M.G.")
                {
                }
                field("BOM Change Mail Group"; Rec."BOM Change Mail Group")
                {
                }
                field("Item Ledger Mail Group"; Rec."Item Ledger Mail Group")
                {
                }
            }
            group(WarehouseShipmentDefaultFields)
            {
                Caption = 'Warehouse Shipment Default Fields';


                field("Def. Customs Officer IPK"; Rec."Def. Customs Officer IPK")
                {
                }
                field("Def. Load Location"; Rec."Def. Load Location")
                {
                }
            }
            group(PriceDataset)
            {
                Caption = 'Price Dataset';

                field("Item Price Dataset Start Date"; Rec."Item Price Dataset Start Date")
                {
                }
            }
        }

    }
    actions
    {
        area(Processing)
        {
            action(FixMultiLinePackageTransferLocations)
            {
                Caption = 'Fix Multi Line Package Transfer Locations';
                ToolTip = 'Executes the Fix Multi Line Package Transfer Locations action.';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = FixedAssets;

                trigger OnAction()
                begin
                    IpekPackageTransMgt.FixPackagesCurrentLocationCode();
                end;
            }
            action(FixGLRegisterNoSeries)
            {
                Caption = 'Fix GL Register No Series';
                ToolTip = 'Executes the action.';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = FixedAssets;

                trigger OnAction()
                begin
                    IpekBasicFunctions.UpdateVoucherNos();
                end;
            }
        }
    }
    var
        IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
        IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";

    trigger OnOpenPage()
    begin
        Rec.InsertIfNotExists();
    end;


}
