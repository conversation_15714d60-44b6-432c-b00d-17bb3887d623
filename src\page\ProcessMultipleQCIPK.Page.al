page 60025 "Process Multiple QC IPK"
{
    ApplicationArea = All;
    Caption = 'Process Multiple QC';
    PageType = Worksheet;
    UsageCategory = None;
    SourceTable = "Multiple QC Approval Line IPK";
    // SourceTableTemporary = true;
    // AutoSplitKey = true;
    // DelayedInsert = true;
    layout
    {

        area(Content)
        {
            field("Palette Barcode"; PaletteBarcode)
            {
                Caption = 'Label';
                ToolTip = 'Specifies the value of the Label field.';

                trigger OnValidate()

                var
                    NoQCFoundErr: Label 'There is no Quality Control Document Package %1.', Comment = '%1=PaletteBarcode';
                begin


                    QualityControlHeaderQCM.SetRange("Package No.", PaletteBarcode);
                    QualityControlHeaderQCM.SetRange(Type, Enum::"Quality Control Type QCM"::Production);
                    QualityControlHeaderQCM.SetRange(Status, Enum::"Quality Control Status QCM"::"Input Pending");
                    Lineno += 1;
                    if QualityControlHeaderQCM.FindFirst() then begin
                        // Clear(TempMultipleQCApprovalLine);
                        MultipleQCApprovalLine.Init();
                        MultipleQCApprovalLine."Entry No." := Lineno;
                        MultipleQCApprovalLine.Insert(true);
                        MultipleQCApprovalLine."Package No." := QualityControlHeaderQCM."Package No.";
                        MultipleQCApprovalLine."Quality Control Document No." := QualityControlHeaderQCM."No.";
                        MultipleQCApprovalLine."Quality Control Doc. Status" := QualityControlHeaderQCM.Status;
                        MultipleQCApprovalLine.Modify(true);
                        // Rec.Reset();
                    end else
                        Error(NoQCFoundErr, PaletteBarcode);
                    PaletteBarcode := '';
                    CurrPage.SetRecord(MultipleQCApprovalLine);
                    CurrPage.Update(false);

                end;
            }


            repeater(General)
            {
                Editable = false;


                field("Package No."; Rec."Package No.")
                {
                }
                field("Quality Control Document No."; Rec."Quality Control Document No.")
                {
                }
                field("Quality Control Doc. Status"; Rec."Quality Control Doc. Status")
                {
                }
            }
        }

    }
    actions
    {
        area(Processing)
        {
            action(Approve)
            {
                Caption = 'Approve';
                ToolTip = 'Executes the Approve action.';
                Promoted = true;
                Image = Approval;
                PromotedIsBig = true;
                PromotedOnly = true;
                PromotedCategory = Process;
                trigger OnAction()
                var
                    ConfirmApprovalMsg: Label 'Do you want to approve quality control documents?';
                    SuccessMsg: Label 'All Quality Control Documents Succesfully Approved.';
                begin
                    if ConfirmManagement.GetResponse(ConfirmApprovalMsg) then
                        repeat
                            QualityControlLineQCM.SetRange("Document No.", Rec."Quality Control Document No.");
                            QualityControlLineQCM.FindSet();

                            repeat
                                QualityControlLineQCM.TestField("Specification Reference", Enum::"Q.C. Spec. Reference Type QCM"::Selection);
                                QualityControlLineQCM.Validate("Selection Result Value", Enum::"Quality Control Selection QCM"::OK);
                                QualityControlLineQCM.Modify(true);
                            until QualityControlLineQCM.Next() = 0;

                        until Rec.Next() = 0;
                    Message(SuccessMsg);
                    CurrPage.Close();

                end;
            }
        }
    }
    trigger OnClosePage()
    begin
        Rec.DeleteAll(true);
    end;

    trigger OnOpenPage()
    begin
        Rec.DeleteAll(true);
    end;

    var
        MultipleQCApprovalLine: Record "Multiple QC Approval Line IPK";
        QualityControlHeaderQCM: Record "Quality Control Header QCM";
        QualityControlLineQCM: Record "Quality Control Line QCM";
        ConfirmManagement: Codeunit "Confirm Management";
        PaletteBarcode: Code[50];
        Lineno: Integer;
}