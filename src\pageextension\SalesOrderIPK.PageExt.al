pageextension 60008 "Sales Order IPK" extends "Sales Order"
{
    layout
    {
        addlast(General)
        {
            field("Freight Amount IPK"; Rec."Freight Amount IPK")
            {
                ApplicationArea = All;
            }
            field("Total Net Weight IPK"; Rec."Total Net Weight IPK")
            {
                ApplicationArea = All;
            }
            field("Total Gross Weight IPK"; Rec."Total Gross Weight IPK")
            {
                ApplicationArea = All;
            }
            field("Total Volume IPK"; Rec."Total Volume IPK")
            {
                ApplicationArea = All;
            }

        }
        // modify("Requested Delivery Date")
        // {
        //     // Visible = false;
        // }
        addafter("Due Date")
        {
            // field("Requested Delivery Date IPK"; Rec."Requested Delivery Date IPK")
            // {
            //     ApplicationArea = All;
            // }
        }
    }
    actions
    {
        addlast("F&unctions")
        {
            action("CalcualteCFRUnitPrices IPK")
            {
                ApplicationArea = All;
                Caption = 'Calculate FOB Unit Prices';
                Image = CalculateCost;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Calculate FOB Unit Prices action.';

                trigger OnAction()
                begin
                    IpekSalesManagement.CalculateFOBUnitPriceFromSalesHeader(Rec);
                end;
            }
        }
    }
    var
        IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
}