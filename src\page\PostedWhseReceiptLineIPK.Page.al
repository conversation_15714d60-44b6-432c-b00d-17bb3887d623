page 60056 "Posted Whse. Receipt Line IPK"
{
    ApplicationArea = All;
    Caption = 'Posted Whse. Receipt Line';
    PageType = List;
    SourceTable = "Posted Whse. Receipt Line";
    UsageCategory = Lists;
    Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the number of the involved entry or record, according to the specified number series.';
                }

                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the number of the item that was received and posted.';
                }
                field("Location Code"; Rec."Location Code")
                {
                    ToolTip = 'Specifies the code of the location where the items were received.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies the description of the item in the line.';
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the quantity that was received.';
                }
                field("Line Weight"; Rec."Unit Weight IPK" * Rec.Quantity)
                {
                    Caption = 'Line Weight';
                    ToolTip = 'Specifies the value of the Line Weight field.';
                }
                field("Vendor No. IPK"; Rec."Vendor No. IPK")
                {
                }
                field(VendorName; VendorName)
                {
                    Caption = 'Vendor Name';
                    ToolTip = 'Specifies the value of the Vendor Name field.';
                }
                field("Vendor Shipment No. IPK"; Rec."Vendor Shipment No. IPK")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    ToolTip = 'Specifies the value of the Posting Date field.';
                }

            }
        }
    }
    trigger OnAfterGetRecord()
    var
        Vendor: Record Vendor;
    begin
        Rec.CalcFields("Vendor No. IPK");
        if Vendor.Get(Rec."Vendor No. IPK") then
            VendorName := Vendor.Name
        else
            VendorName := '';
    end;

    var
        VendorName: Text[100];
}