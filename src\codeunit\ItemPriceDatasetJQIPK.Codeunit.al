codeunit 60031 "Item Price Dataset JQ IPK"
{
    TableNo = "Job Queue Entry";

    trigger OnRun()
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
    begin
        // Step 1: Get configuration from setup
        IpekPamukSetup.GetRecordOnce();
        if IpekPamukSetup."Item Price Dataset Start Date" = 0D then
            Error(StartDateNotConfiguredErr);

        // Step 2: (Removed) Clear all existing records from ItemPriceDatasetIPK table
        // ClearAllExistingRecords();

        // Step 3: Process Production Order items using existing proven logic
        ProcessProductionOrderItems();

        // Step 4: Process Purchase items using existing proven logic
        ProcessPurchaseItems();

        // Log successful completion
        LogJobQueueSuccess();
    end;


    /// <summary>
    /// Processes all items with Replenishment System = Production Order.
    /// Delegates to existing management codeunit and returns count of items processed.
    /// </summary>
    local procedure ProcessProductionOrderItems()
    var
        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
    begin
        // Delegate to existing proven logic in management codeunit
        ItemPriceDatasetMgt.ProcessAllProductionItems();
    end;

    /// <summary>
    /// Processes all items with Replenishment System = Purchase.
    /// Delegates to existing management codeunit and returns count of items processed.
    /// </summary>
    local procedure ProcessPurchaseItems()
    var
        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
    begin
        // Delegate to existing proven logic in management codeunit
        ItemPriceDatasetMgt.ProcessAllPurchaseItems();
    end;



    /// <summary>
    /// Logs successful job completion with processing statistics.
    /// </summary>
    local procedure LogJobQueueSuccess()
    begin
        // No operation
    end;




    var
        StartDateNotConfiguredErr: Label 'Item Price Dataset Start Date is not configured in Ipek Pamuk Setup. Please configure the start date before running the job queue.';
}
