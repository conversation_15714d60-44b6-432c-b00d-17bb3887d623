table 60003 "Load Planning Line IPK"
{
    Caption = 'Load Planning Line';
    DataClassification = CustomerContent;
    LookupPageId = "Load Planning Lines IPK";
    DrillDownPageId = "Load Planning Subpage IPK";

    fields
    {
        field(1; "Warehouse Shipment No."; Code[20])
        {
            Caption = 'Warehouse Shipment No.';
            TableRelation = "Load Header IPK"."Warehouse Shipment No.";
            ToolTip = 'Specifies the value of the Warehouse Shipment No. field.';
        }
        field(2; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            TableRelation = "Load Header IPK"."No.";
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(4; "Warehouse Shipment Line No."; Integer)
        {
            Caption = 'Warehouse Shipment Line No.';
            TableRelation = "Warehouse Shipment Line"."Line No." where("No." = field("Warehouse Shipment No."));
            ToolTip = 'Specifies the value of the Warehouse Shipment Line No. field.';
            trigger OnValidate()
            var
                LoadHeader: Record "Load Header IPK";
                LoadPlanningLine: Record "Load Planning Line IPK";
                WarehouseShipmentLine: Record "Warehouse Shipment Line";
                MixErr: Label 'You cannot add a line for a different item.';
            begin
                LoadHeader.Get(Rec."Warehouse Shipment No.", Rec."Document No.");


                if WarehouseShipmentLine.Get(Rec."Warehouse Shipment No.", Rec."Warehouse Shipment Line No.") then begin
                    if not LoadHeader.Mix then begin
                        LoadPlanningLine.SetRange("Warehouse Shipment No.", Rec."Warehouse Shipment No.");
                        LoadPlanningLine.SetRange("Document No.", Rec."Document No.");
                        LoadPlanningLine.SetFilter("Item No.", '<>%1|<>%2', WarehouseShipmentLine."Item No.", '');
                        if (not LoadPlanningLine.IsEmpty()) and ((LoadPlanningLine.Find('-')) and (LoadPlanningLine.Next() = 0)) then
                            Error(MixErr);
                    end;

                    Rec.Validate("Source No.", WarehouseShipmentLine."Source No.");
                    Rec.Validate("Source Line No.", WarehouseShipmentLine."Source Line No.");
                    Rec.Validate("Item No.", WarehouseShipmentLine."Item No.");
                    Rec.Validate("Variant Code", WarehouseShipmentLine."Variant Code");
                    Rec.Validate(Description, WarehouseShipmentLine.Description);
                    Rec.Validate(Quantity, WarehouseShipmentLine.Quantity);
                    Rec.Validate("Net Weight", WarehouseShipmentLine."Net Weight IPK");
                    Rec.Validate("Gross Weight", WarehouseShipmentLine."Gross Weight IPK");
                    Rec.Validate("Unit Volume", WarehouseShipmentLine."Unit Volume IPK");
                    Rec.Validate("Units per Parcel", WarehouseShipmentLine."Units per Parcel IPK");
                end
                else begin
                    Rec.Validate("Source No.", '');
                    Rec.Validate("Source Line No.", 0);
                    Rec.Validate("Item No.", '');
                    Rec.Validate("Variant Code", '');
                    Rec.Validate(Description, '');
                    Rec.Validate(Quantity, 0);
                    Rec.Validate("Quantity to Load", 0);
                end;
            end;
        }
        field(5; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = "Sales Header"."No." where("Document Type" = const(Order));
            ToolTip = 'Specifies the value of the Source No. field.';
        }
        field(6; "Source Line No."; Integer)
        {
            Caption = 'Source Line No.';
            TableRelation = "Sales Line"."Line No." where("Document Type" = const(Order), "Document No." = field("Source No."));
            ToolTip = 'Specifies the value of the Source Line No. field.';
        }
        field(7; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(8; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(9; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(10; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(11; "Quantity to Load"; Decimal)
        {
            Caption = 'Quantity to Load';
            ToolTip = 'Specifies the value of the Quantity to Load field.';
            trigger OnValidate()
            begin
                if Rec."Quantity to Load" > Rec.Quantity then
                    Error(CannotGreaterErr, Rec.FieldCaption("Quantity to Load"), Rec.FieldCaption(Quantity));

                Rec.CalcFields("Quantity Planned");

                Rec.Validate("Line Net Weight", Rec."Net Weight" * Rec."Quantity to Load");
                Rec.Validate("Line Gross Weight", Rec."Gross Weight" * Rec."Quantity to Load");
                Rec.Validate("Line Volume", Rec."Unit Volume" * Rec."Quantity to Load");
                Rec.Validate("Line Unit Quantity", Rec."Quantity to Load" * Rec."Units per Parcel");

            end;
        }
        field(15; "Quantity Loaded"; Decimal)
        {
            Caption = 'Quantity Loaded';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Load Line IPK".Quantity where("Warehouse Shipment No." = field("Warehouse Shipment No."),
                                                                "Document No." = field("Document No."),
                                                                "Source No." = field("Source No."),
                                                                "Warehouse Shipment Line No." = field("Warehouse Shipment Line No.")));
            ToolTip = 'Specifies the value of the Quantity Loaded field.';

        }

        field(16; "Quantity Planned"; Decimal)
        {
            Caption = 'Quantity Planned';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Load Planning Line IPK"."Quantity to Load" where("Source No." = field("Source No."),
                                                                                    "Source Line No." = field("Source Line No."),
                                                                                    "Warehouse Shipment No." = field("Warehouse Shipment No."),
                                                                                    "Warehouse Shipment Line No." = field("Warehouse Shipment Line No.")));
            ToolTip = 'Specifies the value of the Quantity Planned field.';
        }
        field(13; "Loading Order"; Integer)
        {
            Caption = 'Loading Order';
            ToolTip = 'Specifies the value of the Loading Order field.';
        }
        field(17; "Net Weight"; Decimal)
        {
            Caption = 'Net Weight';
            DecimalPlaces = 0 : 5;

            AllowInCustomizations = Always;
        }
        field(18; "Gross Weight"; Decimal)
        {
            Caption = 'Gross Weight';
            DecimalPlaces = 0 : 5;
            AllowInCustomizations = Always;
        }
        field(19; "Unit Volume"; Decimal)
        {
            Caption = 'Unit Volume';
            AllowInCustomizations = Always;
        }
        field(20; "Line Net Weight"; Decimal)
        {
            Caption = 'Line Net Weight';
            AllowInCustomizations = Always;
        }
        field(21; "Line Gross Weight"; Decimal)
        {
            Caption = 'Line Gross Weight';
            AllowInCustomizations = Always;
        }
        field(22; "Line Volume"; Decimal)
        {
            Caption = 'Line Volume';
            AllowInCustomizations = Always;
        }
        field(12; "Units per Parcel"; Integer)
        {
            Caption = 'Units per Parcel';
            AllowInCustomizations = Always;
        }
        field(14; "Line Unit Quantity"; Integer)
        {
            Caption = 'Line Unit Quantity';
            AllowInCustomizations = Always;
        }
        field(24; "FOB Unit Price"; Decimal)
        {
            AllowInCustomizations = Always;
            Caption = 'FOB Piece Unit Price';
            ToolTip = 'Specifies the value of the FOB Piece Unit Price field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."FOB Piece Unit Price IPK" where("Document Type" = const(Order), "Document No." = field("Source No."), "Line No." = field("Source Line No.")));
        }
        // field(23; "Tariff No."; Text[50])
        // {
        //     Caption = 'Tariff No.';
        //     ToolTip = 'Specifies the value of the Tariff No. field.';
        //     TableRelation = Item."Tariff No." where("No." = field("Item No."));
        // }
        field(25; "Line Unit Qty. Loaded"; Decimal)
        {
            AllowInCustomizations = Always;
            Caption = 'Line Unit Quantity Loaded';
            ToolTip = 'Specifies the value of the FOB Piece Unit Price field.';
            Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = sum("Load Line IPK".Quantity where("Warehouse Shipment No." = field("Warehouse Shipment No."),
            //                                                     "Document No." = field("Document No."),
            //                                                     "Source No." = field("Source No."),
            //                                                     "Warehouse Shipment Line No." = field("Warehouse Shipment Line No.")));
        }

    }
    keys
    {
        key(PK; "Warehouse Shipment No.", "Document No.", "Line No.")
        {
            Clustered = true;
        }
        // key(key2; "Warehouse Shipment No.", "Document No.", "Warehouse Shipment Line No.")
        // {
        //     Unique = true;
        // }
    }
    trigger OnInsert()
    var
        LoadPlanningLine: Record "Load Planning Line IPK";
    begin
        LoadPlanningLine.SetRange("Warehouse Shipment No.", Rec."Warehouse Shipment No.");
        LoadPlanningLine.SetRange("Document No.", Rec."Document No.");
        if LoadPlanningLine.FindLast() then
            Rec."Line No." := LoadPlanningLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    var
        CannotGreaterErr: Label '%1 cannot be greater then %2', Comment = '%1=FieldCaption("Quantity to Load"); %2=FieldCaption(Quantity)';

}