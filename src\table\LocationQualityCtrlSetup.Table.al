table 60017 "Location - Quality Ctrl. Setup"
{
    Caption = 'Location - Quality Control Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Location - Quality Ctrl. Setup";
    LookupPageId = "Location - Quality Ctrl. Setup";

    fields
    {
        field(1; "Quality Control Type"; Enum "Quality Control Type QCM")
        {
            Caption = 'Quality Control Type';
            ToolTip = 'Specifies the value of the Quality Control Type field.';
        }
        field(2; "Quality Control Status"; Enum "Quality Control Status QCM")
        {
            Caption = 'Quality Control Status';
            ToolTip = 'Specifies the value of the Quality Control Status field.';
        }
        field(3; "Allowed Location Code"; Code[10])
        {
            Caption = 'Allowed Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Allowed Location Code field.';
        }


    }
    keys
    {
        key(PK; "Quality Control Type", "Quality Control Status", "Allowed Location Code")
        {
            Clustered = true;
        }
    }
}