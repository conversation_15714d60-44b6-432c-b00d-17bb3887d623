table 60035 "Lot Adjustment Line IPK"
{
    Caption = 'Lot Adjustment List';
    DataClassification = CustomerContent;
    LookupPageId = "Lot Adjustment List IPK";
    DrillDownPageId = "Lot Adjustment List IPK";
    fields
    {
        field(1; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(10; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Line Item No. field.';
        }
        field(11; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the value of the Line Variant Code field.';
        }
        field(4; "Total Qty. on Location"; Decimal)
        {
            Caption = 'Total Qty. on Location';
            ToolTip = 'Specifies the value of the Total Qty. on Location field.';
        }
        field(5; "Physical Quantity"; Decimal)
        {
            Caption = 'Physical Quantity';
            ToolTip = 'Specifies the value of the Physical Quantity field.';
        }
        field(6; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Source Document No. field.';
        }

    }
    keys
    {
        key(PK; "Item No.", "Variant Code", "Lot No.")
        {
            Clustered = true;
        }
        key(Key1; "Item No.", "Variant Code")
        {
        }
    }
}