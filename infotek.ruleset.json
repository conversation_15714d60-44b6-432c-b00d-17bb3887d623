{"name": "Company rule set", "description": "These rules must be respected by all the AL code written within the company.", "rules": [{"id": "LC0010", "action": "None", "justification": "Cyclomatic complexity and Maintainability index."}, {"id": "LC0068", "action": "None", "justification": "Informs the user that there are missing permission to access tabledata."}, {"id": "LC0029", "action": "None", "justification": "Use Compare DateTime method in Type Helper codeunit for DateTime variable comparisons."}, {"id": "LC0084", "action": "None", "justification": "Database read methods, like Record.Get(), returns a boolean indicating whether the record was successfully retrieved. Failing to use this return value can lead to uncaught errors, poor error handling, and a lack of actionable feedback for users when something goes wrong."}, {"id": "LC0079", "action": "None", "justification": "Event Publishers should be local or internal to allow for future parameter extensions."}, {"id": "AA0232", "action": "None", "justification": "You can potentially increase performance if fields that are used in FlowFields are added to SumIndexedFields of the corresponding key."}, {"id": "LC0023", "action": "None", "justification": "Database read methods, like Record.Get(), returns a boolean indicating whether the record was successfully retrieved. Failing to use this return value can lead to uncaught errors, poor error handling, and a lack of actionable feedback for users when something goes wrong."}, {"id": "LC0090", "action": "None", "justification": "This rule analyzes the cognitive complexity of methods to measure how difficult the code is to understand."}, {"id": "LC0091", "action": "None", "justification": "Labels and other variants should be translated for every available translation file language."}]}