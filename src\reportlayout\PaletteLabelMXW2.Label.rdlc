﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="PackageNo">
          <DataField>PackageNo</DataField>
        </Field>
        <Field Name="LocationCodeMXW">
          <DataField>LocationCodeMXW</DataField>
        </Field>
        <Field Name="PackageNoBarCode">
          <DataField>PackageNoBarCode</DataField>
        </Field>
        <Field Name="PackageNoQRCode">
          <DataField>PackageNoQRCode</DataField>
        </Field>
        <Field Name="CreatedAtMXW_PackageNoInformation">
          <DataField>CreatedAtMXW_PackageNoInformation</DataField>
        </Field>
        <Field Name="DocumentNoMXW_PackageNoInformation">
          <DataField>DocumentNoMXW_PackageNoInformation</DataField>
        </Field>
        <Field Name="VariantCode_PackageNoInformation">
          <DataField>VariantCode_PackageNoInformation</DataField>
        </Field>
        <Field Name="SystemModifiedAt_PackageNoInformation">
          <DataField>SystemModifiedAt_PackageNoInformation</DataField>
        </Field>
        <Field Name="SystemModifiedBy_PackageNoInformation">
          <DataField>SystemModifiedBy_PackageNoInformation</DataField>
        </Field>
        <Field Name="SystemId_PackageNoInformation">
          <DataField>SystemId_PackageNoInformation</DataField>
        </Field>
        <Field Name="SystemCreatedBy_PackageNoInformation">
          <DataField>SystemCreatedBy_PackageNoInformation</DataField>
        </Field>
        <Field Name="LotNoMXW_PackageNoInformation">
          <DataField>LotNoMXW_PackageNoInformation</DataField>
        </Field>
        <Field Name="ItemNo_PackageNoInformation">
          <DataField>ItemNo_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformation">
          <DataField>Inventory_PackageNoInformation</DataField>
        </Field>
        <Field Name="Inventory_PackageNoInformationFormat">
          <DataField>Inventory_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="ExpiredInventory_PackageNoInformation">
          <DataField>ExpiredInventory_PackageNoInformation</DataField>
        </Field>
        <Field Name="ExpiredInventory_PackageNoInformationFormat">
          <DataField>ExpiredInventory_PackageNoInformationFormat</DataField>
        </Field>
        <Field Name="Description_PackageNoInformation">
          <DataField>Description_PackageNoInformation</DataField>
        </Field>
        <Field Name="CountryRegionCode_PackageNoInformation">
          <DataField>CountryRegionCode_PackageNoInformation</DataField>
        </Field>
        <Field Name="Comment_PackageNoInformation">
          <DataField>Comment_PackageNoInformation</DataField>
        </Field>
        <Field Name="CertificateNumber_PackageNoInformation">
          <DataField>CertificateNumber_PackageNoInformation</DataField>
        </Field>
        <Field Name="Blocked_PackageNoInformation">
          <DataField>Blocked_PackageNoInformation</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>