﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="ReportName">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>ReportName</Value>
        </Values>
      </DefaultValue>
      <Prompt>ReportName</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="CurrCode">
          <DataField>CurrCode</DataField>
        </Field>
        <Field Name="No_Customer">
          <DataField>No_Customer</DataField>
        </Field>
        <Field Name="Name_Customer">
          <DataField>Name_Customer</DataField>
        </Field>
        <Field Name="Customer_Net_Change">
          <DataField>Customer_Net_Change</DataField>
        </Field>
        <Field Name="Customer_Net_ChangeFormat">
          <DataField>Customer_Net_ChangeFormat</DataField>
        </Field>
        <Field Name="NetChangeLCY_Customer">
          <DataField>NetChangeLCY_Customer</DataField>
        </Field>
        <Field Name="NetChangeLCY_CustomerFormat">
          <DataField>NetChangeLCY_CustomerFormat</DataField>
        </Field>
        <Field Name="CalcTotalAppAmountCustomer">
          <DataField>CalcTotalAppAmountCustomer</DataField>
        </Field>
        <Field Name="CalcTotalAppAmountCustomerFormat">
          <DataField>CalcTotalAppAmountCustomerFormat</DataField>
        </Field>
        <Field Name="No_Vendor">
          <DataField>No_Vendor</DataField>
        </Field>
        <Field Name="Name_Vendor">
          <DataField>Name_Vendor</DataField>
        </Field>
        <Field Name="Vendor_Net_Change">
          <DataField>Vendor_Net_Change</DataField>
        </Field>
        <Field Name="Vendor_Net_ChangeFormat">
          <DataField>Vendor_Net_ChangeFormat</DataField>
        </Field>
        <Field Name="NetChangeLCY_Vendor">
          <DataField>NetChangeLCY_Vendor</DataField>
        </Field>
        <Field Name="NetChangeLCY_VendorFormat">
          <DataField>NetChangeLCY_VendorFormat</DataField>
        </Field>
        <Field Name="CalcTotalAppAmountVendor">
          <DataField>CalcTotalAppAmountVendor</DataField>
        </Field>
        <Field Name="CalcTotalAppAmountVendorFormat">
          <DataField>CalcTotalAppAmountVendorFormat</DataField>
        </Field>
        <Field Name="No_Bank">
          <DataField>No_Bank</DataField>
        </Field>
        <Field Name="Name_Bank">
          <DataField>Name_Bank</DataField>
        </Field>
        <Field Name="Bank_Net_Change">
          <DataField>Bank_Net_Change</DataField>
        </Field>
        <Field Name="Bank_Net_ChangeFormat">
          <DataField>Bank_Net_ChangeFormat</DataField>
        </Field>
        <Field Name="NetChangeLCY_Bank">
          <DataField>NetChangeLCY_Bank</DataField>
        </Field>
        <Field Name="NetChangeLCY_BankFormat">
          <DataField>NetChangeLCY_BankFormat</DataField>
        </Field>
        <Field Name="CalcTotalAppAmountBank">
          <DataField>CalcTotalAppAmountBank</DataField>
        </Field>
        <Field Name="CalcTotalAppAmountBankFormat">
          <DataField>CalcTotalAppAmountBankFormat</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>