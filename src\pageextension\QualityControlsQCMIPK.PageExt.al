pageextension 60027 "Quality Controls QCM IPK" extends "Quality Controls QCM"
{
    layout
    {
        addlast(General)
        {
            field("Source Document No. IPK"; Rec."Source Document No. IPK")
            {
                ApplicationArea = All;
                trigger OnDrillDown()
                begin
                    IpekQualityManagement.OnDrillDown_QualityControlHeader_SourceDocumentNo(Rec);
                end;
            }
            field("Package No. IPK"; Rec."Package No.")
            {
                ApplicationArea = All;
            }
            field("Source Document Vend. Name IPK"; Rec."Source Document Vend. Name IPK")
            {
                ApplicationArea = All;
            }
            field("External Document No. IPK"; Rec."External Document No. IPK")
            {
                ApplicationArea = All;
            }
            field("Quantity IPK"; Rec."Quantity IPK")
            {
                ApplicationArea = All;
            }
        }
    }
    // actions
    // {
    //     addlast(Processing)
    //     {
    //         action("Accept IPK")
    //         {
    //             ApplicationArea = All;
    //             Caption = 'Accept', Comment = 'TRK="Kabul"';
    //             Image = Approve;
    //             Promoted = true;
    //             PromotedCategory = Process;
    //             PromotedIsBig = true;
    //             ToolTip = 'Executes the Accept action.';

    //             trigger OnAction()
    //             var
    //                 QualityControlHeaderQCM: Record "Quality Control Header QCM";
    //             begin
    //                 CurrPage.SetSelectionFilter(QualityControlHeaderQCM);
    //                 IpekQualityManagement.MultipleAcceptance(QualityControlHeaderQCM);
    //             end;
    //         }
    //     }
    // }

    var
        IpekQualityManagement: Codeunit "Ipek Quality Management IPK";
}