page 60047 "Package No. Info. Line FactBox"
{
    ApplicationArea = All;
    Caption = 'Package No. Info. Line FactBox';
    PageType = ListPart;
    SourceTable = "Package No. Info. Line IPK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                Caption = 'General';

                // field("Item No."; Rec."Line Item No.")
                // {
                //     ToolTip = 'Specifies the value of the Item No. field.';
                // }
                // field("Variant Code"; Rec."Line Variant Code")
                // {
                //     ToolTip = 'Specifies the value of the Variant Code field.';
                // }
                // field(Description; Rec.Description)
                // {
                //     ToolTip = 'Specifies the value of the Description field.';
                // }
                field("Lot No."; Rec."Lot No.")
                {
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }

                field(Quantity; Rec.Quantity)
                {
                }
                field(NewQuantity; NewQuantity)
                {
                    Caption = 'New Quantity';
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }

                field("Location Code"; Rec."Location Code")
                {
                }

            }
        }
    }
    var
        NewQuantity: Decimal;
}