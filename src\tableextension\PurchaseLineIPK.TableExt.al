tableextension 60022 "Purchase Line IPK" extends "Purchase Line"
{
    fields
    {
        field(60000; "Whse. Rcpt. Qty-to Receive IPK"; Decimal)
        {
            Caption = 'Warehouse Receipt Qty-to Receive';
            ToolTip = 'Specifies the value of the Warehouse Receipt Qty-to Receive field.';
            DecimalPlaces = 0 : 5;
            trigger OnValidate()
            var
                QuantityErr: Label '%1 can be greater than %2.', Comment = '%1=FieldCaption("Whse. Rcpt. Qty-to Receive IPK"); %2="Purchase Line"."Outstanding Quantity"';
            begin
                if Rec."Whse. Rcpt. Qty-to Receive IPK" > Rec."Outstanding Quantity" then
                    Error(QuantityErr, Rec.FieldCaption("Whse. Rcpt. Qty-to Receive IPK"), Rec."Outstanding Quantity");
            end;
        }
    }
}