table 60010 "Prod.Order Line - Lot No. IPK"
{
    Caption = 'Prod.Order Line - Lot No.';
    DataClassification = CustomerContent;
    LookupPageId = "Prod.Order Line - Lot No. IPK";
    DrillDownPageId = "Prod.Order Line - Lot No. IPK";

    fields
    {
        field(1; Status; Enum "Production Order Status")
        {
            Caption = 'Status';
            Editable = false;
            ToolTip = 'Specifies the value of the Status field.';
            AllowInCustomizations = Never;
        }
        field(2; "Prod. Order No."; Code[20])
        {
            Caption = 'Production Order No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Prod Order No. field.';
        }
        field(3; "Prod. Order Line No."; Integer)
        {
            Caption = 'Prod. Order Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Prod. Order Line No. field.';
        }
        field(4; "Start Date-Time"; DateTime)
        {
            Caption = 'Start Date-Time';
            ToolTip = 'Specifies the value of the Start Date-Time field.';
            trigger OnValidate()
            begin
                if Rec."Start Date-Time" <> 0DT then
                    Rec.Validate("End Date-Time", Rec."Start Date-Time" + (8 * 3600000))//8 Hour
                else
                    Rec.Validate("End Date-Time", 0DT);
            end;
        }
        field(5; "End Date-Time"; DateTime)
        {
            Caption = 'End Date-Time';
            ToolTip = 'Specifies the value of the End Date-Time field.';
            trigger OnValidate()
            begin
                Rec.CalcFields("Item No.");

                if Rec."End Date-Time" <> 0DT then
                    Rec.Validate("Lot No.", IpekProductionManagement.GetNextLotNoFromItemNo(Rec."Item No."))
                else
                    Rec.Validate("Lot No.", '');
            end;
        }
        field(6; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(7; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            AllowInCustomizations = Never;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Line"."Item No." where(Status = field(Status), "Prod. Order No." = field("Prod. Order No."), "Line No." = field("Prod. Order Line No.")));
        }
    }
    keys
    {
        // key(PK; Status, "Prod. Order No.", "Prod. Order Line No.", "Lot No.")
        // {
        //     Clustered = true;
        // }
        key(PK; Status, "Prod. Order No.", "Prod. Order Line No.", "Start Date-Time")
        {
            Clustered = true;
        }
    }
    var
        IpekProductionManagement: Codeunit "Ipek Production Management IPK";
}