pageextension 60036 "Production Order List IPK" extends "Production Order List"
{
    layout
    {
        addafter(Quantity)
        {
            field("Remaining Quantity IPK"; Rec."Remaining Quantity IPK")
            {
                ApplicationArea = All;
            }
            field("Last Production Date-Time IPK"; Rec."Last Production Date-Time IPK")
            {
                ApplicationArea = All;
            }
            field("Package Count IPK"; Rec."Package Count IPK")
            {
                ApplicationArea = All;
            }
            field("Consumption Location Code IPK"; Rec."Consumption Location Code IPK")
            {
                ApplicationArea = All;
            }
        }
        addafter("No.")
        {
            field("Source Document No. IPK"; Rec."Source Document No. IPK")
            {
                ApplicationArea = All;
            }
            field("Customer Name IPK"; Rec."Customer Name IPK")
            {
                ApplicationArea = All;
            }
        }
        modify("Routing No.")
        {
            Visible = false;
        }
        modify("Starting Date-Time")
        {
            Visible = false;
        }
        modify("Ending Date-Time")
        {
            Visible = false;
        }
        modify("Search Description")
        {
            Visible = false;
        }
    }
}