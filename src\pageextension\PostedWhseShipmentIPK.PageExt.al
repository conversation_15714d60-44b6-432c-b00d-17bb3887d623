pageextension 60016 "Posted Whse. Shipment IPK" extends "Posted Whse. Shipment"
{
    layout
    {
        addafter("Assignment Time")
        {

            field("Load No. IPK"; Rec."Load No. IPK")
            {
                ApplicationArea = All;
            }
        }
        addafter(General)
        {
            group("Export IPK")
            {
                Caption = 'Export Information';

                field("Total Net Weight IPK"; Rec."Total Net Weight IPK")
                {
                    ApplicationArea = All;
                }
                field("Vessel Name IPK"; Rec."Vessel Name IPK")
                {
                    ApplicationArea = All;
                }
                field("Voyage No. IPK"; Rec."Voyage No. IPK")
                {
                    ApplicationArea = All;
                }
                field("PoL Code IPK"; Rec."PoL Code IPK")
                {
                    ApplicationArea = All;
                }
                field("PoA Code IPK"; Rec."PoA Code IPK")
                {
                    ApplicationArea = All;
                }
                field("Line IPK"; Rec."Line IPK")
                {
                    ApplicationArea = All;
                }
                field("Forwarder IPK"; Rec."Forwarder IPK")
                {
                    ApplicationArea = All;
                }
                field("ETS IPK"; Rec."ETS IPK")
                {
                    ApplicationArea = All;
                }
                field("ETA IPK"; Rec."ETA IPK")
                {
                    ApplicationArea = All;
                }
                field("Cutt-off Date IPK"; Rec."Cutt-off Date IPK")
                {
                    ApplicationArea = All;
                }
                field("Load Count IPK"; Rec."Load Count IPK")
                {
                    ApplicationArea = All;
                }
                field("Salesperson Code IPK"; Rec."Salesperson Code IPK")
                {
                    ApplicationArea = All;
                }
                field("Priority Shipment IPK"; Rec."Priority Shipment IPK")
                {
                    ApplicationArea = All;
                }
                field("Total Gross Weight IPK"; Rec."Total Gross Weight IPK")
                {
                    ApplicationArea = All;
                }
                field("Total Volume IPK"; Rec."Total Volume IPK")
                {
                    ApplicationArea = All;
                }
                field("Export No. IPK"; Rec."Export No. IPK")
                {
                    ApplicationArea = All;
                }
                field("Customs Code IPK"; Rec."Customs Code IPK")
                {
                    ApplicationArea = All;
                }
                field("Customs Officer IPK"; Rec."Customs Officer IPK")
                {
                    ApplicationArea = All;
                }
                field("Flag IPK"; Rec."Flag IPK")
                {
                    ApplicationArea = All;
                }
                field("E-Export No. IPK"; Rec."E-Export No. IPK")
                {
                    ApplicationArea = All;
                }
                field("Total Unit Quantity IPK"; Rec."Total Unit Quantity IPK")
                {
                    ApplicationArea = All;
                }
                field("PoL Description IPK"; Rec."PoL Description IPK")
                {
                    ApplicationArea = All;
                }
                field("PoA Description IPK"; Rec."PoA Description IPK")
                {
                    ApplicationArea = All;
                }
                field("Customs Description IPK"; Rec."Customs Description IPK")
                {
                    ApplicationArea = All;
                }
                field("Location County IPK"; Rec."Location County IPK")
                {
                    ApplicationArea = All;
                }
                field("Vessel Shipping Agent Code IPK"; Rec."Vessel Shipping Agent Code IPK")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
}