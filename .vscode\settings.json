{"CRS.ReorganizeByNamespace": false, "al.rootNamespace": "Infotek", "alOutline.additionalMandatoryAffixesPatterns": [" INF", " IPK"], "alOutline.defaultDataClassification": "Customer<PERSON><PERSON>nt", "alOutline.enableCodeCopFixes": true, "alOutline.fixCaseRemovesQuotesFromDataTypeIdentifiers": true, "alOutline.fixCodeCopMissingParenthesesOnSave": true, "alOutline.noEmptyLinesAtTheEndOfWizardGeneratedFiles": true, "alOutline.openDefinitionInNewTab": true, "linterCop.load-pre-releases": true, "alVarHelper.ignoreALSuffix": "IPK", "alNavigator.ignoreALSuffix": "IPK", "CRS.OnSaveAlFileAction": "Reorganize", "CRS.ObjectNameSuffix": " IPK", "al.codeAnalyzers": ["${CodeCop}", "${UICop}", "${PerTenantExtensionCop}", "${analyzerFolder}BusinessCentral.LinterCop.dll"], "al.ruleSetPath": "infotek.ruleset.json", "editor.snippetSuggestions": "bottom", "ALTB.snippetTargetLanguage": "TRK", "xliffSync.snippetTargetLanguage": "TRK", "alOutline.activeBuildConfiguration": ""}