page 60014 "Prod. Order Line Details IPK"
{
    ApplicationArea = All;
    Caption = 'Prod. Order Line Details';
    PageType = List;
    SourceTable = "Prod. Order Line Detail IPK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    ToolTip = 'Specifies the value of the Package No. field.';
                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.Get('', '', Rec."Package No.");
                        Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                    end;
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the value of the Item No. field.';
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    ToolTip = 'Specifies the value of the Variant Code field.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies the value of the Description field.';
                }
                field("Lot No."; Rec."Lot No.")
                {
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }
                field("Location Code"; Rec."Location Code")
                {
                    ToolTip = 'Specifies the value of the Location Code field.';
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the value of the Quantity field.';
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(Posted; Rec.Posted)
                {
                    ToolTip = 'Specifies the value of the Posted field.';
                }
                field("Palette No."; Rec."Palette No.")
                {
                    ToolTip = 'Specifies the value of the Palette No. field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateOutputJournal)
            {
                Caption = 'Create Output Journals';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Output Journals action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    ProdOrderLineDetail: Record "Prod. Order Line Detail IPK";
                //ProdOrderLineDetail2: Record "Prod. Order Line Detail SMK";
                begin
                    CurrPage.SetSelectionFilter(ProdOrderLineDetail);
                    ProdOrderLineDetail.FindSet();
                    repeat
                        IpekProductionManagement.CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail);
                        IpekProductionManagement.CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail);
                        IpekProductionManagement.PostConsumptionAndOutputJournals(ProdOrderLineDetail);
                    until ProdOrderLineDetail.Next() = 0;
                end;
            }
            action(CreateConsJournal)
            {
                Caption = 'Create Consumption Journals';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Consumption Journals action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    ProdOrderLineDetail: Record "Prod. Order Line Detail IPK";
                //ProdOrderLineDetail2: Record "Prod. Order Line Detail SMK";
                begin
                    CurrPage.SetSelectionFilter(ProdOrderLineDetail);
                    ProdOrderLineDetail.FindSet();
                    repeat
                        IpekProductionManagement.CreateConsumptionJournalsFromProdOrderLineDetail(Rec);
                    until ProdOrderLineDetail.Next() = 0;
                end;
            }
            action(CreatePalette)
            {
                Caption = 'Create Palette';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Consumption Palette action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    InventorySetup: Record "Inventory Setup";
                    PackageNoInfoLine: Record "Package No. Info. Line IPK";
                    PackageNoInformation: Record "Package No. Information";
                    ProdOrderLineDetail: Record "Prod. Order Line Detail IPK";
                    NoSeries: Codeunit "No. Series";
                begin
                    InventorySetup.GetRecordOnce();
                    CurrPage.SetSelectionFilter(ProdOrderLineDetail);
                    if ProdOrderLineDetail.FindSet() then
                        if Confirm('%1 of selected rows will be used to create a palette are you sure ?', false, ProdOrderLineDetail.Count()) then begin
                            PackageNoInformation.Init();
                            PackageNoInformation."Item No." := '';
                            PackageNoInformation."Variant Code" := '';
                            PackageNoInformation."Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.");
                            PackageNoInformation.Insert(true);
                            PackageNoInformation.Validate("Pallet IPK", true);
                            PackageNoInformation.Modify(true);
                            repeat
                                PackageNoInformation.CalcFields("Total Quantity IPK");
                                PackageNoInfoLine.Init();
                                PackageNoInfoLine."Item No." := PackageNoInformation."Item No.";
                                PackageNoInfoLine."Variant Code" := PackageNoInformation."Variant Code";
                                PackageNoInfoLine."Package No." := PackageNoInformation."Package No.";
                                PackageNoInfoLine.Quantity := PackageNoInformation."Total Quantity IPK";
                                PackageNoInfoLine.Insert(true);
                                PackageNoInfoLine.Validate("Source Package No. IPK", ProdOrderLineDetail."Package No.");
                                PackageNoInfoLine.Modify(true);

                                ProdOrderLineDetail."Palette No." := PackageNoInformation."Package No.";
                                ProdOrderLineDetail.Modify(true);
                            until ProdOrderLineDetail.Next() = 0;
                        end;
                end;
            }
        }
    }
    var
        IpekProductionManagement: Codeunit "Ipek Production Management IPK";
}