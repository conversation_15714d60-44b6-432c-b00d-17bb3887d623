table 60016 "Multiple QC Approval Line IPK"
{

    Caption = 'Multiple Quality Control Lines';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(2; "Quality Control Document No."; Code[20])
        {
            Caption = 'Quality Control Document No.';
            ToolTip = 'Specifies the value of the Quality Control Document No. field.';
        }
        field(3; "Quality Control Doc. Status"; Enum "Quality Control Status QCM")
        {
            Caption = 'Quality Control Document Status';
            ToolTip = 'Specifies the value of the Quality Control Document Status field.';
        }
        field(4; "Entry No."; Integer)
        {
            Caption = 'Line';
            AllowInCustomizations = Always;
            DataClassification = ToBeClassified;
        }

    }
    keys
    {
        // key(PK; "Package No.")
        // {
        //     Clustered = true;
        // }
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
}