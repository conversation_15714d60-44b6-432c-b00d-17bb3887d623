page 60072 "Inventory Adjustment Subpage"
{
    ApplicationArea = All;
    Caption = 'Inventory Adjustment Line';
    PageType = ListPart;
    SourceTable = "Inventory Adjustment Line IPK";
    // UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                }
                field("Location Code IPK"; Rec."Location Code")
                {
                }
                field("Current Location Code"; Rec."Current Location Code")
                {
                    ToolTip = 'Specifies the value of the Current Location Code field.';
                }
                field("Line Item No. IPK"; Rec."Line Item No.")
                {
                }
                field("Line Variant Code IPK"; Rec."Line Variant Code")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Visible = false;
                }
                field("Total Quantity IPK"; Rec."Total Quantity")
                {
                }
                field("Physical Quantity IPK"; Rec."Physical Quantity")
                {
                    ToolTip = 'Specifies the value of the Physical Quantity field.';
                }
                field("Adjustment Type"; Rec."Adjustment Type")
                {
                }
            }
        }
    }
}