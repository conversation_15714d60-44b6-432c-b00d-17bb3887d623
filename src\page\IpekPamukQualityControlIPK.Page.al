page 60022 "Ipek Pamuk Quality Control IPK"
{
    ApplicationArea = All;
    Caption = 'Production Quality Control', Comment = 'Üretim Kalite Kontrol';
    // CaptionML = TRK = 'Kalite Kontrol', ENU = 'Ipek Pamuk Quality Control';
    PageType = CardPart;
    SourceTable = "Ipek Pamuk Activity Cue IPK";
    RefreshOnActivate = true;
    layout
    {

        area(Content)
        {
            group(ApproveProductionQualityControlGroup)
            {
                Caption = 'Quality Control', Comment = 'Kalite Kontrol Belgesi Onayla.';
                // CaptionML = TRK = 'Kalite Kontrol Belgesi Onayla.', ENU = 'Approve Production Quality Control Group';
                ShowCaption = false;
                cuegroup(Activities)
                {
                    Caption = 'Quality Control Activities';
                    ShowCaption = false;
                    actions
                    {
                        action(ApproveProductionQualityControl)
                        {
                            Caption = 'Approve Quality Control', Comment = 'Kalite Kontrol Belgesi Onayla.';
                            // CaptionML = TRK = 'Kalite Kontrol Belgesi Onayla.', ENU = 'Approve Production Quality Control Group';
                            Image = TileGreen;
                            RunObject = page "Process Quality Control Doc.";
                            ToolTip = 'Executes the Approve Production Quality Control action.';

                        }

                    }

                }
            }

            group(ApproveMultipleProductionQualityControlGroup)
            {
                Caption = 'Quality Control', Comment = 'Kalite Kontrol Belgesi Onayla.';
                // CaptionML = TRK = 'Kalite Kontrol Belgesi Onayla.', ENU = 'Approve Production Quality Control Group';
                ShowCaption = false;
                cuegroup(ActivitiesMultiple)
                {
                    Caption = 'Quality Control Activities';
                    ShowCaption = false;
                    actions
                    {
                        action(ApproveMultipleProductionQualityControl)
                        {
                            Caption = 'Multiple Quality Control', Comment = 'Kalite Kontrol Belgesi Onayla.';
                            // CaptionML = TRK = 'Kalite Kontrol Belgesi Onayla.', ENU = 'Approve Production Quality Control Group';
                            Image = TileGreen;
                            RunObject = page "Process Multiple QC IPK";
                            ToolTip = 'Executes the Multiple Production Quality Control action.';

                        }

                    }

                }
            }
            cuegroup(QualityControl)
            {
                Caption = 'Quality Control';
                field("Production QCD"; Rec."Production QCD")
                {
                    DrillDownPageId = "Quality Controls QCM";
                    ToolTip = 'Specifies the value of the Production field.';
                }
                field("Purchased QCD"; Rec."Purchase QCD")
                {
                    DrillDownPageId = "Quality Controls QCM";
                    ToolTip = 'Specifies the value of the Purchase field.';
                }
            }
        }
    }
    trigger OnOpenPage()
    begin
        Rec.Reset();
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert(false);
            Commit();//
        end;
    end;
}