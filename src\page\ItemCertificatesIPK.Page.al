page 60008 "Item Certificates IPK"
{
    ApplicationArea = All;
    Caption = 'Item Certificates';
    PageType = List;
    SourceTable = "Item Certificate IPK";
    UsageCategory = Administration;
    DelayedInsert = true;
    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field("No."; Rec."No.")
                {
                }
                field("Certificate Authority"; Rec."Certificate Authority")
                {
                }
                field("Item Type"; Rec."Item Type")
                {
                }
                field("Certificate ID"; Rec."Certificate ID")
                {
                }
            }
        }
    }
}