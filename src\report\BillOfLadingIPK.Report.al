report 60005 "Bill Of Lading IPK"
{
    ApplicationArea = All;
    Caption = 'Bill Of Lading';
    UsageCategory = ReportsAndAnalysis;
    //DefaultLayout = Excel;
    //ExcelLayout = 'test.xlsx';
    dataset
    {
        dataitem("Load Card IPK"; "Load Header IPK")
        {
            RequestFilterFields = "No.";

            column(Domestic_Shipping_Agent_Code; "Domestic Shipping Agent Code")//Nakliyeci:LoadCard->Domestic Shipping agent Code, name, telno, şimdilik kalıyor
            {
            }
            column(Load_Place; "Load Location")//Yükleme yeri diye bir alan LoadCarda açılacak daha açılacak Load Card Text[50]
            {
            }
            column(TruckLicensePlate_LoadCardIPK; "Truck License Plate")//AraçPlaka:LoadCard->plaka
            {
            }
            column(TrailerLicensePlate_LoadCardIPK; "Trailer License Plate")//AraçPlaka:LoadCard->plaka
            {
            }
            column(Notes_LoadCardIPK; Notes)
            {
            }
            column(EShipmentNo_LoadCardIPK; "E-Shipment No.")
            {
            }
            column(WarehouseShipmentNo_LoadCardIPK; "Warehouse Shipment No.")
            {
            }
            column(PostingDate_LoadCardIPK; "Posting Date")
            {
            }
            column(Container_No_; "Container No.")
            {
            }
            dataitem("Load Planning Line IPK"; "Load Planning Line IPK")
            {
                DataItemLink = "Warehouse Shipment No." = field("Warehouse Shipment No."),
                                "Document No." = field("No.");
                CalcFields = "Quantity Loaded", "FOB Unit Price";
                column(ItemNo_LoadPlanningLineIPK; "Item No.")
                {
                }
                column(Description_LoadPlanningLineIPK; Description)
                {
                }
                column(QuantityLoaded_LoadPlanningLineIPK; "Quantity Loaded")
                {
                }
                column(Line_Unit_Quantity; "Line Unit Quantity")
                {
                }
                column(LineGrossWeight_LoadPlanningLineIPK; "Line Gross Weight")
                {
                }
                column(LineNetWeight_LoadPlanningLineIPK; "Line Net Weight")
                {
                }
                column(LineVolume_LoadPlanningLineIPK; "Line Volume")
                {
                }
                // column(FOBUnitPrice_LoadPlanningLineIPK; "FOB Unit Price" / "Units per Parcel")
                // {
                // }
                // column(CFRUnitPrice; IpekSalesManagement.CalculateCFRUnitPriceFromLoadPlanningLine("Load Planning Line IPK"))
                // {
                // }
                // column(FOBLineAmount; "FOB Unit Price" * "Quantity Loaded")
                // {
                // }
                // column(CFRLineAmount; IpekSalesManagement.CalculateCFRUnitPriceFromLoadPlanningLine("Load Planning Line IPK") * "Quantity Loaded")
                // {
                // }
                dataitem("Sales Line"; "Sales Line")
                {
                    DataItemLink = "Document No." = field("Source No."), "Line No." = field("Source Line No.");

                    column(CFR_Line_Amount_IPK; "FOB Line Amount IPK")
                    {
                    }
                    column(CFR_Piece_Unit_Price_IPK; "Freight Inc PieceUnitPrice IPK")
                    {
                    }
                    column(FOB_Piece_Unit_Price_IPK; "FOB Piece Unit Price IPK")
                    {
                    }
                    column(Line_Amount; "Line Amount")
                    {
                    }
                }
                dataitem(Item; Item)
                {
                    DataItemLink = "No." = field("Item No.");
                    column(Tariff_No_; "Tariff No.")
                    {
                    }
                }
            }
            dataitem("Company Information"; "Company Information")
            {
                column(Address_CompanyInformation; Address)
                {
                }
                column(Address2_CompanyInformation; "Address 2")
                {
                }
                column(City_CompanyInformation; City)
                {
                }
                column(County_CompanyInformation; County)
                {
                }
                column(Name_CompanyInformation; Name)
                {
                }
            }
            dataitem("Warehouse Shipment Header"; "Warehouse Shipment Header")
            {
                DataItemLink = "No." = field("Warehouse Shipment No.");
                CalcFields = "PoL Description IPK",
                             "PoA Description IPK",
                             "Customs Description IPK",
                             "Location County IPK";
                column(Export_No__IPK; "Export No. IPK")
                {
                }
                column(Location_County_IPK; "Location County IPK")
                {
                }
                column(Customs_Description_IPK; "Customs Description IPK")
                {
                }
                column(ShipmentMethodCode_ShipToCustomer; "Shipment Method Code")
                {
                }
                column(CustomsIPK_WarehouseShipmentHeader; "Customs Code IPK")
                {
                }
                column(Vessel_Name_IPK; "Vessel Name IPK")//Gemi adı : Vessel Name
                {
                }
                column(VoyageNoIPK_WarehouseShipmentHeader; "Voyage No. IPK")
                {
                }
                column(PoA_IPK; "PoA Code IPK")
                {
                }
                column(PoA_Description_IPK; "PoA Description IPK")
                {
                }
                column(PoL_IPK; "PoL Code IPK")
                {
                }
                column(PoL_Description_IPK; "PoL Description IPK")
                {
                }
                column(FlagIPK_WarehouseShipmentHeader; "Flag IPK")
                {
                }
                column(CuttoffDateIPK_WarehouseShipmentHeader; "Cutt-off Date IPK")
                {
                }
                column(Customs_Officer_IPK; "Customs Officer IPK")
                {
                }
                dataitem("Shipping Agent"; "Shipping Agent")
                {
                    DataItemLink = Code = field("Shipping Agent Code");

                    column(Shipping_Agent_Name; Name)
                    {
                    }
                    dataitem(Vendor; Vendor)
                    {
                        DataItemLink = "No." = field("Vendor No. INF");

                        column(Shipping_Agent_Vendor_Name; Name)
                        {
                        }
                    }
                }
                dataitem("Vessel Shipping Agent"; "Shipping Agent")
                {
                    DataItemLink = Code = field("Vessel Shipping Agent Code IPK");

                    column(Vessel_Shipping_Agent_Name; Name)
                    {
                    }
                    dataitem(Vessel_Vendor; Vendor)
                    {
                        DataItemLink = "No." = field("Vendor No. INF");

                        column(Vessel_Shipping_Agent_Vendor_Name; Name)
                        {
                        }
                    }
                }
                dataitem("Shipping Agent Services"; "Shipping Agent Services")
                {
                    DataItemLink = "Shipping Agent Code" = field("Shipping Agent Code"), Code = field("Shipping Agent Service Code");

                    column(Shipping_Agent_Service_Description; Description)
                    {
                    }
                }
                dataitem("Ship-to Address"; "Ship-to Address")
                {
                    DataItemLink = "Customer No." = field("Source No. INF"),
                                    Code = field("Ship-to Code INF");
                    column(Name_ShiptoAddress; Name)
                    {
                    }
                    column(Address_ShipToCustomer; Address)
                    {
                    }
                    column(Address2_ShipToCustomer; "Address 2")
                    {
                    }
                    column(City_ShipToCustomer; City)
                    {
                    }
                    column(County_ShipToCustomer; County)
                    {
                    }
                }
                dataitem(SellToCustomer; Customer)
                {
                    DataItemLink = "No." = field("Source No. INF");

                    column(Address_SellToCustomer; Address)
                    {
                    }
                    column(Address2_SellToCustomer; "Address 2")
                    {
                    }
                    column(City_SellToCustomer; City)
                    {
                    }
                    column(County_SellToCustomer; County)
                    {
                    }
                    column(Name_SellToCustomer; Name)
                    {
                    }
                }
                dataitem("Warehouse Comment Line"; "Warehouse Comment Line")
                {
                    DataItemLink = "No." = field("No.");
                    column(Comment; Comment)
                    {
                    }
                }
                dataitem("Warehouse Shipment Line"; "Warehouse Shipment Line")
                {
                    DataItemLink = "No." = field("No.");
                    column(Source_No_; "Source No.")
                    {
                    }
                    dataitem("Sales Header"; "Sales Header")
                    {
                        DataItemLink = "No." = field("Source No.");
                        column(Your_Reference; "Your Reference")
                        {
                        }
                        dataitem("Company Bank Account"; "Bank Account")
                        {
                            DataItemLink = "No." = field("Company Bank Account Code");
                            column(Bank_Name; Name)
                            {
                            }
                        }
                        // dataitem("Sales Line"; "Sales Line")
                        // {
                        //     DataItemLink = "Document Type" = field("Document Type"),
                        //                     "Document No." = field("No.");
                        //     DataItemTableView = where(Type = const(Item));

                        //     column(UnitPrice_SalesLine; "Unit Price")
                        //     {
                        //     }
                        //     dataitem("Item Charge Assignment (Sales)"; "Item Charge Assignment (Sales)")
                        //     {
                        //         DataItemLink = "Applies-to Doc. Type" = field("Document Type"),
                        //                         "Applies-to Doc. No." = field("No."),
                        //                         "Applies-to Doc. Line No." = field("Line No.");

                        //         column(AmounttoAssign_ItemChargeAssignmentSales; "Amount to Assign")
                        //         {
                        //         }
                        //     }
                        // }
                        column(Payment_Method_Description; Payment_Method_Description)
                        {
                        }
                        column(Payment_Method_Code; Payment_Method_Code)
                        {
                        }
                        column(Payment_Terms_Description; Payment_Terms_Description)
                        {
                        }
                        column(Payment_Terms_Code; Payment_Terms_Code)
                        {
                        }

                        trigger OnAfterGetRecord()
                        var
                            PaymentMethod: Record "Payment Method";
                            PaymentMethodTranslation: Record "Payment Method Translation";
                            PaymentTerms: Record "Payment Terms";
                            PaymentTermTranslation: Record "Payment Term Translation";
                        begin

                            if "Sales Header"."Language Code" <> '' then begin
                                PaymentTermTranslation.Get("Sales Header"."Payment Terms Code", "Sales Header"."Language Code");
                                PaymentMethodTranslation.Get("Sales Header"."Payment Method Code", "Sales Header"."Language Code");
                                Payment_Terms_Code := PaymentTermTranslation."Payment Term";
                                Payment_Terms_Description := PaymentTermTranslation.Description;
                                Payment_Method_Code := PaymentMethodTranslation."Payment Method Code";
                                Payment_Method_Description := PaymentMethodTranslation.Description;
                            end
                            else begin
                                PaymentTerms.Get("Sales Header"."Payment Terms Code");
                                PaymentMethod.Get("Sales Header"."Payment Method Code");
                                Payment_Terms_Code := PaymentTerms.Code;
                                Payment_Terms_Description := PaymentTerms.Description;
                                Payment_Method_Code := PaymentMethod.Code;
                                Payment_Method_Description := PaymentMethod.Description;
                            end;
                        end;
                    }
                }
            }
        }
    }
    var
        Payment_Method_Code: Code[10];
        Payment_Terms_Code: Code[10];
        Payment_Method_Description: Text[100];
        Payment_Terms_Description: Text[100];
}