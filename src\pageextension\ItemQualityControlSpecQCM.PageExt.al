pageextension 60028 "Item Quality Control Spec. QCM" extends "Item Quality Control Spec. QCM"
{

    layout
    {
        modify(Type)
        {
            trigger OnAfterValidate()
            begin
                if Rec.Type = Rec.Type::Production then begin
                    Rec.Validate("Specification Reference", Rec."Specification Reference"::Selection);
                    Rec.Validate("Selection Value", Rec."Selection Value"::OK);
                end;
            end;
        }
        modify("Specification Reference")
        {
            Editable = not (Rec.Type = Rec.Type::Production);
        }
    }
}