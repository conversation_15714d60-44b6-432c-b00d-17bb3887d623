pageextension 60024 "Quality Control QCM IPK" extends "Quality Control QCM"
{
    layout
    {
        addafter(SystemCreatedAt)
        {

            field("Source Document No. IPK"; Rec."Source Document No. IPK")
            {
                ApplicationArea = All;
                Editable = false;
                trigger OnDrillDown()
                begin
                    IpekQualityManagement.OnDrillDown_QualityControlHeader_SourceDocumentNo(Rec);
                end;
            }
            field("Source Document Line No. IPK"; Rec."Source Document Line No. IPK")
            {
                ApplicationArea = All;
                Editable = false;
            }
            field("Item Unit Per Parcel IPK"; Rec."Item Unit Per Parcel IPK")
            {
                ApplicationArea = All;
                Editable = false;
                Visible = not (Rec."Source Document Type IPK" = Rec."Source Document Type IPK"::"Warehouse Recipt");
            }
            field("Source Document Vend. Name IPK"; Rec."Source Document Vend. Name IPK")
            {
                ApplicationArea = All;
            }


            field("External Document No. IPK"; Rec."External Document No. IPK")
            {
                ApplicationArea = All;
            }
            field("Quantity IPK"; Rec."Quantity IPK")
            {
                ApplicationArea = All;
            }

        }
        addbefore(Comment)
        {

            field("Score IPK"; Rec."Score IPK")
            {
                ApplicationArea = All;
            }
        }

    }

    var
        IpekQualityManagement: Codeunit "Ipek Quality Management IPK";
}