page 60003 "Load Lines IPK"
{
    ApplicationArea = All;
    Caption = 'Load Lines';
    PageType = List;
    SourceTable = "Load Line IPK";
    UsageCategory = History;
    Extensible = true;
    Editable = true;
    InsertAllowed = false;
    ModifyAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Warehouse Shipment No.")
                {
                    ToolTip = 'Specifies the value of the Warehouse Shipment No. field.';
                }
                field("Document Line No."; Rec."Warehouse Shipment Line No.")
                {
                    ToolTip = 'Specifies the value of the Warehouse Shipment Line No. field.';
                }
                field("Source Document No."; Rec."Source No.")
                {
                    ToolTip = 'Specifies the value of the Source No. field.';
                }
                field("Your Reference"; Rec."Your Reference")
                {
                }
                field("Contaier No."; Rec."Document No.")
                {
                    ToolTip = 'Specifies the value of the Document No. field.';
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Label No.")
                {
                    ToolTip = 'Specifies the value of the Label No. field.';
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Total Read Quantity"; Rec."Total Read Quantity")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Remaining Quantity At Loc."; Rec."Remaining Quantity At Loc.")
                {
                }
                field("Produced At"; Rec."Produced At")
                {
                }
            }
        }
    }
}