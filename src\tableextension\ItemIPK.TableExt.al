tableextension 60000 "Item IPK" extends Item
{
    fields
    {
        field(60000; "Brand IPK"; Code[10])
        {
            Caption = 'Brand';
            TableRelation = Manufacturer.Code;
            ToolTip = 'Specifies the value of the Brand field.';
        }
        field(60001; "Customer No. IPK"; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
            begin
                if Customer.Get(Rec."Customer No. IPK") then
                    Rec."Customer Name IPK" := Customer.Name
                else
                    Rec."Customer Name IPK" := '';
            end;
        }
        field(60002; "Customer Name IPK"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(60003; "Pieces on Pallet IPK"; Integer)
        {
            Caption = 'Pieces on Pallet';
            ToolTip = 'Specifies the value of the Pieces on Pallet field.';
        }
        field(60004; "Item Category Description IPK"; Text[100])
        {
            Caption = 'Item Category Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Category".Description where(Code = field("Item Category Code")));
            ToolTip = 'Specifies the value of the Item Category Description field.';
        }
        field(60005; "Export/Domestic IPK"; Enum "Export/Domestic IPK")
        {
            Caption = 'Export/Domestic';
            ToolTip = 'Specifies the value of the Export/Domestic field.';
        }
        field(60006; "Default Output Location IPK"; Code[10])
        {
            Caption = 'Default Output Location';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Default Output Location field.';
        }
    }
    trigger OnAfterDelete()
    var
        ItemCertificateLineIPK: Record "Item Certificate Line IPK";
    begin
        ItemCertificateLineIPK.SetRange("Item No.", Rec."No.");
        ItemCertificateLineIPK.DeleteAll(true);
    end;

    trigger OnModify()
    begin
        Rec.TestField("Item Category Code");
    end;

}