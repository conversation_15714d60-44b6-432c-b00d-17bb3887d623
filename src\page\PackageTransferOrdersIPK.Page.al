page 60020 "Package Transfer Orders IPK"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Orders';
    PageType = List;
    SourceTable = "Package Transfer Header IPK";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "Package Transfer Order IPK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    ToolTip = 'Specifies the value of the Transfer-to Code field.';
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Shipped; Rec.Shipped)
                {
                }
                field(Posted; Rec.Received)
                {
                }
                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                }
                field("Palette Count"; Rec."Palette Count")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}