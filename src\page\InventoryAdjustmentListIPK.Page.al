page 60049 "Inventory Adjustment List IPK"
{
    ApplicationArea = All;
    Caption = 'Inventory Adjustment List';
    PageType = List;
    SourceTable = "Inventory Adjustment Header";
    UsageCategory = Lists;
    Editable = false;
    CardPageId = "Inventory Adjustment IPK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(MergeAdjustmentLists)
            {
                Caption = 'Merge Adjustment Lists', Comment = 'TRK="MergeAdjustmentLists"';
                // Enabled = not Rec."Ready For Post";
                Image = AdjustEntries;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Merge Adjustment Lists action.';

                trigger OnAction()
                var
                    InventoryAdjustmentHeader: Record "Inventory Adjustment Header";
                    NewInventoryAdjustmentHeader: Record "Inventory Adjustment Header";
                    InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
                    NewInventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
                    MergeInventoryAdjustment: Page "Merge Inventory Adjustment IPK";
                    MergeLocationCode: Code[10];
                    EndDate: DateTime;
                    StartDate: DateTime;
                begin
                    MergeInventoryAdjustment.LookupMode(true);
                    if MergeInventoryAdjustment.RunModal() = Action::LookupOK then begin
                        StartDate := MergeInventoryAdjustment.GetStartDate();
                        EndDate := MergeInventoryAdjustment.GetEndDate();
                        MergeLocationCode := MergeInventoryAdjustment.GetMergeLocationCode();
                        if (StartDate <> 0DT) and (EndDate <> 0DT) and (MergeLocationCode <> '') then begin
                            InventoryAdjustmentHeader.SetFilter(SystemCreatedAt, '>%1&<%2', StartDate, EndDate);
                            InventoryAdjustmentHeader.SetRange("Location Code", MergeLocationCode);
                            InventoryAdjustmentHeader.SetRange(Merged, false);
                            if InventoryAdjustmentHeader.FindSet() then begin
                                NewInventoryAdjustmentHeader.Init();
                                NewInventoryAdjustmentHeader."Location Code" := MergeLocationCode;
                                NewInventoryAdjustmentHeader.Insert(true);

                                InventoryAdjustmentHeader.Merged := true;
                                InventoryAdjustmentHeader.Modify(true);
                                repeat
                                    if InventoryAdjustmentHeader."No." = NewInventoryAdjustmentHeader."No." then
                                        break;
                                    InventoryAdjustmentLine.SetRange("Document No.", InventoryAdjustmentHeader."No.");
                                    InventoryAdjustmentLine.FindSet(false);
                                    repeat
                                        NewInventoryAdjustmentLine.Init();
                                        NewInventoryAdjustmentLine.TransferFields(InventoryAdjustmentLine);
                                        NewInventoryAdjustmentLine."Document No." := NewInventoryAdjustmentHeader."No.";
                                        // NewInventoryAdjustmentLine := InventoryAdjustmentLine;
                                        NewInventoryAdjustmentLine.Insert(true);
                                    until InventoryAdjustmentLine.Next() = 0;
                                until InventoryAdjustmentHeader.Next() = 0;
                            end;
                        end
                    end;
                    CurrPage.Update(false);
                end;
            }
        }
    }
}