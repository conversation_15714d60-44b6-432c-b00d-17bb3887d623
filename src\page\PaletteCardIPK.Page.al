page 60044 "Palette Card IPK"
{
    ApplicationArea = All;
    Caption = 'Palette Card';
    PageType = Card;
    SourceTable = "Package No. Information";
    UsageCategory = Lists;
    layout
    {
        area(Content)
        {




            field("Location Code IPK"; Rec."Location Code IPK")
            {
                TableRelation = Location.Code;
            }
            field("Machine/Line No. IPK"; Rec."Machine/Line No. IPK")
            {
            }
            field("Producted At (DateTime) IPK"; Rec."Created At IPK")
            {
            }
            field("Production Order No. IPK"; Rec."Document No. IPK")
            {
            }
            field("Warehouse Shipment No. IPK"; Rec."Warehouse Shipment No. IPK")
            {
            }
            field("New Package No. IPK"; Rec."New Package No. IPK")
            {
            }
            field("Pallet IPK"; Rec."Pallet IPK")
            {
            }

            group(Barcode)
            {

                Caption = 'Barcode';
                ShowCaption = false;
                field(PackageBarcode; Barcode)
                {
                    Caption = 'Package Barcode';
                    ToolTip = 'Specifies the value of the Package Barcode field.';
                    trigger OnValidate()
                    var
                        LoadPlanningLine: Record "Load Planning Line IPK";
                        PackageNoInfoLine2: Record "Package No. Info. Line IPK";
                        PackageNoInformation: Record "Package No. Information";
                        ThisPackageAlreadyInsideErr: Label 'This package already inside of %1', Comment = '%1="Package No. Info. Line IPK"."Package No."';
                    begin
                        if Barcode = '' then
                            exit;

                        LoadPlanningLine.SetAutoCalcFields("Quantity Loaded");
                        PackageNoInformation.Get('', '', Barcode);

                        PackageNoInfoLine2.SetRange("Source Package No. IPK", Barcode);
                        if PackageNoInfoLine2.IsEmpty() then begin
                            PackageNoInfoLine2.Init();
                            PackageNoInfoLine2."Item No." := '';
                            PackageNoInfoLine2."Variant Code" := '';
                            PackageNoInfoLine2."Package No." := Rec."Package No.";
                            PackageNoInfoLine2.Insert(true);
                            PackageNoInfoLine2."Source Package No. IPK" := Barcode;
                            PackageNoInfoLine2.Quantity := PackageNoInformation."Total Quantity IPK";
                            PackageNoInfoLine2.Modify(true);
                        end
                        else
                            Error(ThisPackageAlreadyInsideErr, PackageNoInfoLine2."Package No.");

                    end;
                }
            }
            part(Lines; "Package No. Info. Subpage IPK")
            {
                Caption = 'Lines';
                SubPageLink = "Source Package No. IPK" = field("Package No.");
                UpdatePropagation = Both;
                // Editable = false;

            }
        }

    }
    actions
    {
        area(Creation)
        {
            action("PrintLabel IPK")
            {
                AccessByPermission = tabledata "Serial No. Information" = I;
                ApplicationArea = ItemTracking;
                Caption = 'Print Label';
                Image = Print;
                ToolTip = 'Executes the Print Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PaletteLabel: Report "Palette Label IPK";
                begin
                    PackageNoInformation := Rec;
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PaletteLabel.SetTableView(PackageNoInformation);
                    PaletteLabel.RunModal();
                end;
            }
        }
    }
    var
        Barcode: Code[20];
}