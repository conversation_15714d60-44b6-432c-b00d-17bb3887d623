page 60068 "Package No. Info. Lines Lite"
{
    ApplicationArea = All;
    Caption = 'Package No. Info. Lines Lite';
    PageType = List;
    SourceTable = "Package No. Info. Line IPK";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Line Item No."; Rec."Line Item No.")
                {
                    ToolTip = 'Specifies the value of the Line Item No. field.';
                }
                field("Line Variant Code"; Rec."Line Variant Code")
                {
                    ToolTip = 'Specifies the value of the Line Variant Code field.';
                }
                field("Lot No."; Rec."Lot No.")
                {
                    ToolTip = 'Specifies the value of the Lot No. field.';
                }
                field(Quantity; Rec.Quantity)
                {
                }

            }
        }
    }
}