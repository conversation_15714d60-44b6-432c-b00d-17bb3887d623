page 60016 "Ipek Pamuk Activities IPK"
{
    ApplicationArea = All;
    Caption = 'Production Activities', Comment = 'Üretim Süreçleri';
    // CaptionML = TRK = 'Üretim Süreçleri', ENU = 'Production Activities';
    PageType = CardPart;
    RefreshOnActivate = true;
    SourceTable = "Ipek Pamuk Activity Cue IPK";

    layout
    {
        area(Content)
        {
            group(CreatePalletGroup)
            {
                ShowCaption = false;
                cuegroup(Activities)
                {
                    Caption = 'Activities';
                    ShowCaption = false;
                    actions
                    {
                        action(CreatePallet)
                        {
                            Caption = 'Create Pallet', Comment = 'Palet Oluştur';
                            // CaptionML = TRK = 'Palet Oluştur', ENU = 'Create Pallet';
                            Image = TileGreen;
                            // RunObject = page "Production Order List";
                            ToolTip = 'Executes the Create Pallet action.';
                            trigger OnAction()
                            begin
                                IpekProductionManagement.CreatePackageFromRoleCenter();
                            end;
                        }

                    }

                }
            }
            group(CreateWaterjetPalletGroup)
            {
                ShowCaption = false;
                cuegroup(CreateWaterjetPalletCueGroup)
                {
                    Caption = 'CreateWaterjetPalletCueGroup';
                    ShowCaption = false;
                    actions
                    {
                        action(CreateWaterjetPallet)
                        {
                            Caption = 'Waterjet-Fitil Palet Oluştur', Comment = 'Waterjet-Fitil Palet Oluştur';
                            // CaptionML = TRK = 'Palet Oluştur', ENU = 'Create Pallet';
                            Image = TileGreen;
                            // RunObject = page "Production Order List";
                            ToolTip = 'Executes the Create Pallet action.';
                            trigger OnAction()
                            begin
                                IpekProductionManagement.CreatePaletteFromRoleCenter();
                            end;
                        }

                    }

                }
            }

            cuegroup(Production)
            {
                Caption = 'Production';

                field("Released Production Orders"; Rec."Released Production Orders")
                {
                    DrillDownPageId = "Production Order List";
                }
            }
            cuegroup(BomApprovals)
            {
                Caption = 'Bom Approvals';

                field("Pending Approvals"; Rec."Pending Bom Approvals")
                {
                    DrillDownPageId = "Production BOM List";
                }
            }

        }
    }
    trigger OnOpenPage()
    var
        UserSetup: Record "User Setup";
        UserText: Text[50];
    begin
        Rec.Reset();
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert(false);
            Commit();//
        end;
        UserText := CopyStr(UserId(), 1, MaxStrLen(UserText));
        Rec.SetFilter("User ID Filter", UserText);
        UserSetup.Get(UserText);
        Rec.SetFilter("Waiting Pac. Tran. Loc. Filt.", UserSetup."Role Center Loc. Filter IPK");
    end;


    var
        IpekProductionManagement: Codeunit "Ipek Production Management IPK";
}