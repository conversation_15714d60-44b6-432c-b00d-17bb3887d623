page 60026 "Package Transfer Details MXW"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Details';
    PageType = List;
    SourceTable = "Package Transfer Line MXW";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    ApplicationArea = All;
                }

                field("Item No."; Rec."Item No.")
                {
                    ApplicationArea = All;
                }

                field("Variant Code"; Rec."Variant Code")
                {
                    ApplicationArea = All;
                }

                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                }

                field(Quantity; Rec.Quantity)
                {
                    ApplicationArea = All;
                }

                field("Quantity To Transfer"; Rec."Quantity To Transfer")
                {
                    ApplicationArea = All;
                }

                field("Lot No."; Rec."Lot No.")
                {
                    ApplicationArea = All;
                }

                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                    ApplicationArea = All;
                }

                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    ApplicationArea = All;
                }

                field("Current Package Location"; Rec."Current Package Location")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
}
