page 60038 "Consumption Differences IPK"
{
    ApplicationArea = All;
    Caption = 'Consumption Differences';
    PageType = List;
    SourceTable = "Prod. Order Component";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the number of the item that is a component in the production order component list.';
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    ToolTip = 'Specifies the variant of the item on the line.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies a description of the item on the line.';
                }
                field("Category Code IPK"; Rec."Category Code IPK")
                {
                    ToolTip = 'Specifies the value of the Category Code field.';
                }
                field("Category Description IPK"; Rec."Category Description IPK")
                {
                    ToolTip = 'Specifies the value of the Category Description field.';
                }
                field("Location Code"; Rec."Location Code")
                {
                    ToolTip = 'Specifies the location where the component is stored. It is copied from the corresponding field on the production order line.';
                }
                field("Quantity At Location IPK"; Rec."Quantity At Location IPK")
                {
                }
                field("Consumed Quantity IPK"; Rec."Consumed Quantity IPK")
                {
                }
                field("Additional Cons. Qty. IPK"; Rec."Additional Cons. Qty. IPK")
                {
                }
            }
        }
    }
    // actions
    // {
    //     area(Processing)
    //     {
    //         action(CreateConsJournal)
    //         {
    //             ApplicationArea = All;
    //             Caption = 'Create Consumption Journals';
    //             Promoted = true;
    //             PromotedCategory = Process;
    //             PromotedIsBig = true;
    //             Image = PostOrder;
    //             ToolTip = 'Executes the Create Consumption Journals action.';
    //             PromotedOnly = true;

    //             trigger OnAction()
    //             var
    //                 // ProdOrderLineDetail: Record "Prod. Order Line Detail IPK";
    //             //ProdOrderLineDetail2: Record "Prod. Order Line Detail SMK";
    //             begin
    //                 // Message('.');
    //                 // CurrPage.SetSelectionFilter(ProdOrderLineDetail);
    //                 // ProdOrderLineDetail.FindSet();
    //                 // repeat
    //                 //     IpekProductionManagement.CreateConsumptionJournalsFromProdOrderLineDetail(Rec);
    //                 // until ProdOrderLineDetail.Next() = 0;
    //             end;
    //         }
    //     }
    // }
    var
    // IpekProductionManagement: Codeunit "Ipek Production Management IPK";
}