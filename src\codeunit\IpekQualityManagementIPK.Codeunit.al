codeunit 60005 "Ipek Quality Management IPK"
{
    procedure GenerateQualityControlDocumentFromWareHauseRecptLine(var Rec: Record "Warehouse Receipt Line")
    var
        // LotNoInformation: Record "Lot No. Information";
        QualityControlTypeQCM: Enum "Quality Control Type QCM";

    begin
        // LotNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Lot No. IPK");

        CreateQualityControlDocumentFromWarehouseReciptLine(Rec, QualityControlTypeQCM::Purchase);

    end;

    procedure GetMailListByQCStatus(Status: Enum "Quality Control Status QCM"): Text[250]
    var
        QCMailMap: Record "Q.C. Mail Setup IPK";
        MailAddressStatusMsg: Label 'There is no mail adress set for this status, please check Quality Control Mail Setup';
        MailList: Text[250];
    begin
        QCMailMap.SetRange("Quality Control Status", Status);
        if QCMailMap.FindSet() then begin
            repeat
                if MailList = '' then
                    MailList := (QCMailMap."E-Mail")
                else
                    MailList := MailList + (';' + QCMailMap."E-Mail");
            until QCMailMap.Next() = 0;
            exit(MailList);
        end
        else
            Message(MailAddressStatusMsg);

    end;

    procedure CreateQualityControlDocumentFromWarehouseReciptLine(var WarehouseReciptLine: Record "Warehouse Receipt Line"; QualityControlType: Enum "Quality Control Type QCM")
    var
        PurchaseHeader: Record "Purchase Header";
        QualityControlHeader: Record "Quality Control Header QCM";
        WarehouseReceiptHeader: Record "Warehouse Receipt Header";
    begin
        PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, WarehouseReciptLine."Source No.");

        WarehouseReceiptHeader.Get(WarehouseReciptLine."No.");
        // WarehouseReceiptHeader.CalcFields("Vendor Name IPK");
        QualityControlHeader.Init();
        QualityControlHeader.Insert(true);
        QualityControlHeader.Validate(Type, QualityControlType);
        QualityControlHeader.Validate("Item No.", WarehouseReciptLine."Item No.");
        QualityControlHeader.Validate("Variant Code", WarehouseReciptLine."Variant Code");
        QualityControlHeader.Validate("Item Description", WarehouseReciptLine.Description);
        QualityControlHeader.Validate(Date, WorkDate());
        // QualityControlHeader.Validate("Lot No.", WarehouseReciptLine."Lot No. IPK");
        QualityControlHeader."Lot No." := WarehouseReciptLine."Lot No. IPK";

        QualityControlHeader.Validate("Source Document No. IPK", WarehouseReciptLine."No.");

        QualityControlHeader.Validate("Source Document Line No. IPK", WarehouseReciptLine."Line No.");
        QualityControlHeader.Validate("Source Document Type IPK", QualityControlHeader."Source Document Type IPK"::"Warehouse Recipt");
        QualityControlHeader.Validate("External Document No. IPK", WarehouseReceiptHeader."Vendor Shipment No.");
        QualityControlHeader.Validate("Source Document Vend. Name IPK", PurchaseHeader."Buy-from Vendor Name");

        QualityControlHeader.Validate("Quantity IPK", WarehouseReciptLine.Quantity);
        QualityControlHeader.Modify(true);

        // WarehouseReciptLine.Validate("Certificate Number", QualityControlHeader."No.");
        WarehouseReciptLine.Validate("Quality Control Doc. No. IPK", QualityControlHeader."No.");
        // WarehouseReciptLine.Modify(true);

        QualityControlManagementQCM.PopulateQualityControlLines(QualityControlHeader);
        //Message('Quality control document created.');
    end;

    procedure CreateQualityControlForProdOrderLineDetail(var ProdOrderLineDetail: Record "Prod. Order Line Detail IPK")
    var
        QualityControlHeader: Record "Quality Control Header QCM";
    begin
        QualityControlHeader.Init();
        QualityControlHeader.Insert(true);
        QualityControlHeader.Validate(Type, Enum::"Quality Control Type QCM"::Production);
        QualityControlHeader.Validate("Item No.", ProdOrderLineDetail."Item No.");
        QualityControlHeader.Validate("Variant Code", ProdOrderLineDetail."Variant Code");
        QualityControlHeader.Validate("Item Description", ProdOrderLineDetail.Description);
        QualityControlHeader.Validate(Date, WorkDate());
        QualityControlHeader."Lot No." := ProdOrderLineDetail."Lot No.";
        QualityControlHeader."Package No." := ProdOrderLineDetail."Package No.";


        QualityControlHeader.Validate("Source Document No. IPK", ProdOrderLineDetail."Prod. Order No.");
        QualityControlHeader.Validate("Source Document Line No. IPK", ProdOrderLineDetail."Prod. Order Line No.");
        QualityControlHeader.Validate("Source Document Type IPK", QualityControlHeader."Source Document Type IPK"::"Production Order");

        QualityControlHeader.Modify(true);

        QualityControlManagementQCM.PopulateQualityControlLines(QualityControlHeader);
    end;

    procedure AutoTransferAfterWarehouseReciptPost(WarehouseReceiptLine: Record "Warehouse Receipt Line")
    // Location: Record Location;
    var

        Item: Record Item;
        ItemCategory: Record "Item Category";
        QualityControlMngtSetupQCM: Record "Quality Control Mngt Setup QCM";
    begin
        WarehouseReceiptLine.CalcFields("Quality Control Doc. Stat. IPK");
        case WarehouseReceiptLine."Quality Control Doc. Stat. IPK" of
            "Quality Control Status QCM"::Acceptance, "Quality Control Status QCM"::"Conditional Acceptance":
                begin
                    Item.Get(WarehouseReceiptLine."Item No.");
                    ItemCategory.Get(Item."Item Category Code");
                    ItemCategory.TestField("Default Receive Location IPK");

                    // PackageTransferHeader.Get(CreatePackageTransferDocument(WarehouseReceiptLine, ItemCategory."Default Receive Location IPK"));
                    // IpekPackageTransMgt.ShipAndRecievePackagetransferHeader(PackageTransferHeader);

                    CreatePackageTransferDocumentForAutoTransfer(WarehouseReceiptLine, ItemCategory."Default Receive Location IPK");
                end;
            "Quality Control Status QCM"::Rejection:
                begin

                    QualityControlMngtSetupQCM.GetRecordOnce();
                    // PackageTransferHeader.Get(CreatePackageTransferDocument(WarehouseReceiptLine, QualityControlMngtSetupQCM."Rejection Location IPK"));
                    // IpekPackageTransMgt.ShipAndRecievePackagetransferHeader(PackageTransferHeader);
                    CreatePackageTransferDocumentForAutoTransfer(WarehouseReceiptLine, QualityControlMngtSetupQCM."Rejection Location IPK");
                end;
            "Quality Control Status QCM"::Quarantine:
                begin

                    QualityControlMngtSetupQCM.GetRecordOnce();
                    // PackageTransferHeader.Get(CreatePackageTransferDocument(WarehouseReceiptLine, QualityControlMngtSetupQCM."Rejection Location IPK"));
                    // IpekPackageTransMgt.ShipAndRecievePackagetransferHeader(PackageTransferHeader);
                    CreatePackageTransferDocumentForAutoTransfer(WarehouseReceiptLine, QualityControlMngtSetupQCM."Quarantine Location IPK");
                end;
        end;
    end;

    // procedure PostPackageTransferHeader(PackageTransferHeader: Record "Package Transfer Header IPK")
    // begin
    //     IpekPackageTransMgt.ShipAndRecievePackagetransferHeader(Rec);
    // end;
    // procedure PostAutoTransferOrders(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    // var
    //     PackageTransferHeader: Record "Package Transfer Header IPK";
    // begin
    //     PackageTransferHeader.SetRange("Source Document No.", WarehouseReceiptHeader."No.");
    //     PackageTransferHeader.SetRange(Received, false);
    //     PackageTransferHeader.SetRange(Shipped, false);
    //     PackageTransferHeader.FindSet();
    //     Commit();
    //     repeat
    //         IpekPackageTransMgt.CreateAndPostItemReclassificationJournal(PackageTransferHeader, true);
    //     until PackageTransferHeader.Next() = 0;
    // end;

    procedure CreatePackageTransferDocumentForAutoTransfer(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; LocationCode: Code[10]): Code[20]
    var
        PackageTransferHeader: Record "Package Transfer Header IPK";
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl IPK";
    begin


        PackageTransferHeader.Init();
        PackageTransferHeader.Insert(true);
        PackageTransferHeader.Validate("Transfer-to Code", LocationCode);
        PackageTransferHeader.Validate("Source Document No.", WarehouseReceiptLine."No.");
        PackageTransferHeader.Modify(true);

        WarehouseReceiptLineDtl.SetRange("Document No.", WarehouseReceiptLine."No.");
        WarehouseReceiptLineDtl.SetRange("Document Line No.", WarehouseReceiptLine."Line No.");
        WarehouseReceiptLineDtl.SetRange(Recieved, false);

        WarehouseReceiptLineDtl.FindSet(false);
        repeat
            PackageTransferHeader.Validate(Barcode, WarehouseReceiptLineDtl."Package No.");
            PackageTransferHeader.Validate("Package Transfer Type", PackageTransferHeader."Package Transfer Type"::"Quality Control");
        // WarehouseReceiptLineDtl.Validate(Recieved, true);
        // WarehouseReceiptLineDtl.Modify(true);
        until WarehouseReceiptLineDtl.Next() = 0;
        exit(PackageTransferHeader."No.");

    end;

    procedure CreatePackageTransferDocumentFromQualityControlHeader(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; LocationCode: Code[10]): Code[20]
    var
        PackageTransferHeader: Record "Package Transfer Header IPK";
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl IPK";
    begin


        PackageTransferHeader.Init();
        PackageTransferHeader.Insert(true);
        PackageTransferHeader.Validate("Transfer-to Code", LocationCode);
        PackageTransferHeader.Validate("Source Document No.", WarehouseReceiptLine."No.");
        PackageTransferHeader.Modify(true);

        WarehouseReceiptLineDtl.SetRange("Document No.", WarehouseReceiptLine."No.");
        WarehouseReceiptLineDtl.SetRange("Document Line No.", WarehouseReceiptLine."Line No.");
        WarehouseReceiptLineDtl.SetRange(Recieved, false);

        WarehouseReceiptLineDtl.FindSet(false);
        repeat
            PackageTransferHeader.Validate(Barcode, WarehouseReceiptLineDtl."Package No.");
            PackageTransferHeader.Validate("Package Transfer Type", PackageTransferHeader."Package Transfer Type"::"Quality Control");
        // WarehouseReceiptLineDtl.Validate(Recieved, true);
        // WarehouseReceiptLineDtl.Modify(true);
        until WarehouseReceiptLineDtl.Next() = 0;
        exit(PackageTransferHeader."No.");

    end;

    // procedure MultipleAcceptance(var QualityControlHeaderQCM: Record "Quality Control Header QCM")
    // var
    //     DocumentCount: Integer;
    //     AcceptedCountMsg: Label '%1 Quality Control Document(s) Accepted.', Comment = '%1="Quality Control Header QCM".Count';
    // begin
    //     QualityControlHeaderQCM.FindSet(true);
    //     DocumentCount := QualityControlHeaderQCM.Count();
    //     repeat
    //         QualityControlHeaderQCM.Validate(Status, QualityControlHeaderQCM.Status::Acceptance);
    //         QualityControlHeaderQCM.Modify(true);
    //     until QualityControlHeaderQCM.Next() = 0;

    //     Message(AcceptedCountMsg, DocumentCount);
    // end;

    procedure OnDrillDown_QualityControlHeader_SourceDocumentNo(QualityControlHeader: Record "Quality Control Header QCM")
    var
        PostedWhseReceiptHeader: Record "Posted Whse. Receipt Header";
        ProductionOrder: Record "Production Order";
        WarehouseReceiptHeader: Record "Warehouse Receipt Header";
        PageManagement: Codeunit "Page Management";
    begin

        case QualityControlHeader.Type of
            "Quality Control Type QCM"::Production:
                begin
                    ProductionOrder.SetRange("No.", QualityControlHeader."Source Document No. IPK");
                    if ProductionOrder.FindFirst() then
                        PageManagement.PageRun(ProductionOrder);
                end;
            "Quality Control Type QCM"::Purchase:
                begin
                    if WarehouseReceiptHeader.Get(QualityControlHeader."Source Document No. IPK") then
                        PageManagement.PageRun(WarehouseReceiptHeader);

                    PostedWhseReceiptHeader.SetRange("Whse. Receipt No.", QualityControlHeader."Source Document No. IPK");
                    if PostedWhseReceiptHeader.FindFirst() then
                        PageManagement.PageRun(PostedWhseReceiptHeader);
                end;
            "Quality Control Type QCM"::Sales:
                ;//
        end;

        // case true of
        //     ProductionOrder.Get(ProductionOrder.Status::Released, QualityControlHeader."Source Document No. IPK"):
        //         PageManagement.PageRun(ProductionOrder);
        //     WarehouseReceiptHeader.Get(QualityControlHeader."Source Document No. IPK"):
        //         PageManagement.PageRun(WarehouseReceiptHeader);
        //     else begin
        //         PostedWhseReceiptHeader.SetRange("Whse. Receipt No.", QualityControlHeader."Source Document No. IPK");
        //         if PostedWhseReceiptHeader.FindFirst() then
        //             PageManagement.PageRun(PostedWhseReceiptHeader);
        //     end;
        // end;
    end;

    var
        QualityControlManagementQCM: Codeunit "Quality Control Management QCM";
}