codeunit 60004 "Ipek Basic Functions IPK"
{
    Permissions = tabledata "G/L Register" = M;
    procedure GetItemDescriptionFromItemNo(ItemNo: Code[20]): Text[100]
    var
        Item: Record Item;
    begin
        if not Item.Get(ItemNo) then
            exit('');

        exit(Item.Description);
    end;

    procedure GetItemDescriptionFromVariantCode(ItemNo: Code[20]; VariantCode: Code[10]): Text[100]
    var
        ItemVariant: Record "Item Variant";
    begin
        if not ItemVariant.Get(ItemNo, VariantCode) then
            exit('');
        exit(ItemVariant.Description);
    end;

    procedure IsPackageSpecificTrackingActive(ItemNo: Code[20]): Boolean
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
    begin
        if not Item.Get(ItemNo) then
            exit(false);

        if not ItemTrackingCode.Get(Item."Item Tracking Code") then
            exit(false);

        exit(ItemTrackingCode."Package Specific Tracking");
    end;

    procedure CreatePackageNoInformationFromItemJournalLine(ItemJournalLine: Record "Item Journal Line")
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageNoInformation: Record "Package No. Information";
        InventoryAdjustment: Codeunit "Inventory Adjustment IPK";
    begin
        if ItemJournalLine."Palette Barcode IPK" = '' then
            exit;

        if PackageNoInformation.Get('', '', ItemJournalLine."Palette Barcode IPK") then begin
            InventoryAdjustment.AdjustPackageLineQuantities(ItemJournalLine);
            exit;
        end;
        PackageNoInformation.Init();
        PackageNoInformation."Package No." := ItemJournalLine."Palette Barcode IPK";
        PackageNoInformation.Validate("Location Code IPK", ItemJournalLine."Location Code");
        PackageNoInformation.Insert(true);

        PackageNoInfoLine.Init();
        PackageNoInfoLine."Item No." := PackageNoInformation."Item No.";
        PackageNoInfoLine."Variant Code" := PackageNoInformation."Variant Code";
        PackageNoInfoLine."Package No." := PackageNoInformation."Package No.";
        PackageNoInfoLine.Insert(true);
        PackageNoInfoLine.Validate("Line Item No.", ItemJournalLine."Item No.");
        PackageNoInfoLine.Validate("Line Variant Code", ItemJournalLine."Variant Code");
        PackageNoInfoLine.Validate(Description, ItemJournalLine.Description);
        PackageNoInfoLine.Validate(Quantity, ItemJournalLine.Quantity);
        PackageNoInfoLine.Validate("Lot No.", ItemJournalLine."Lot No.");
        PackageNoInfoLine.Modify(true);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Item Ledger Entry", OnAfterCopyTrackingFromItemJnlLine, '', false, false)]
    local procedure "Item Ledger Entry_OnAfterCopyTrackingFromItemJnlLine"(var ItemLedgerEntry: Record "Item Ledger Entry"; ItemJnlLine: Record "Item Journal Line")
    begin
        CreatePackageNoInformationFromItemJournalLine(ItemJnlLine);
    end;

    procedure PopulateItemNoFromDocumentNo(var PackageCreation: Record "Package Creation IPK" temporary)
    var
        ProdOrderLine: Record "Prod. Order Line";
        ProductionOrder: Record "Production Order";
    begin
        if ProductionOrder.Get(ProductionOrder.Status::Released, PackageCreation."Document No.") then begin
            ProdOrderLine.SetRange(Status, ProductionOrder.Status);
            ProdOrderLine.SetRange("Prod. Order No.", ProductionOrder."No.");
            ProdOrderLine.SetRange("Line No.", 10000);
            if (ProdOrderLine.Find('-')) and (ProdOrderLine.Next() = 0) then
                PackageCreation.Validate("Document Line No.", ProdOrderLine."Line No.")
            else
                PackageCreation.Validate("Document Line No.", 0)
        end
        else begin
            PackageCreation."Document No." := '';
            PackageCreation.Validate("Document Line No.", 0);
        end;
    end;

    procedure GetUserNameFromSecurityId(UserSecurityID: Guid): Code[50]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."User Name");
        exit('');
    end;

    procedure WarehouseShipmentHeader_SellToSourceNo_OnAfterValidate_GetLocationCodeFromCustomerOrVendor(var Rec: Record "Warehouse Shipment Header")
    var
        Customer: Record Customer;
        Vendor: Record Vendor;
    begin
        case Rec."E-Sh. Source Type INF" of
            Enum::"Whse. Source Type INF"::Sales:
                if Customer.Get(Rec."Sell-to Source No. INF") then begin
                    Rec.Validate("Location Code", Customer."Location Code");
                    Rec.Validate("Sell-to Customer Name INF", Customer.Name);
                end;
            Enum::"Whse. Source Type INF"::"Purchase Return":
                if Vendor.Get(Rec."Sell-to Source No. INF") then
                    Rec.Validate("Location Code", Vendor."Location Code");
        end;
    end;

    procedure WarehouseShipmentHeader_SellToSourceNo_OnAfterValidate_AssignFields(var Rec: Record "Warehouse Shipment Header")
    var
        Customer: Record Customer;
    begin
        Rec.Validate("Is Export IPK", IsExportFromCustomerNo(Rec."Sell-to Source No. INF"));

        if Customer.Get(Rec."Sell-to Source No. INF") then
            Rec.Validate("Single Shipment Per Order IPK", Customer."Single Shipment Per Order IPK");
    end;

    procedure IsExportFromCustomerNo(CustomerNo: Code[20]): Boolean
    var
        Customer: Record Customer;
    begin
        if not Customer.Get(CustomerNo) then
            exit(false);

        EInvoiceSetup.Get();
        if EInvoiceSetup."E-Invoice Profile for Export" = '' then
            exit(false);

        if Customer."E-Invoice Profile ID INF" <> EInvoiceSetup."E-Invoice Profile for Export" then
            exit(false);

        exit(true);
    end;

    procedure GetUserFullNameFromSecurityId(UserSecurityID: Guid): Text[80]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."Full Name");
        exit('');
    end;

    procedure DeletePackNoLines(var PackageNoInformation: Record "Package No. Information")
    var
        DeletedPackageNoInfoLines: Record "DeletedPackageNoInfoLines IPK";
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
    begin
        PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
        if PackageNoInfoLine.FindSet() then begin
            DeletedPackageNoInfoLines.Init();
            DeletedPackageNoInfoLines.TransferFields(PackageNoInfoLine);
            DeletedPackageNoInfoLines.Insert(true);
            PackageNoInfoLine.DeleteAll(true);
        end;
    end;

    procedure CreateItemJournalLineBeforeDeletion(var PackageNoInfoLine: Record "Package No. Info. Line IPK")
    var
        // ConfirmManagement: Codeunit "Confirm Management";
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ItemJournalLineNo: Integer;
    begin
        LastItemJournalLine.SetRange("Journal Template Name", 'MADDGNL');
        LastItemJournalLine.SetRange("Journal Batch Name", 'SAYIM-1');

        if not LastItemJournalLine.FindLast() then
            ItemJournalLineNo := 10000
        else
            ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;

        PackageNoInfoLine.CalcFields("Location Code");

        ItemJournalLine.Init();
        ItemJournalLine."Line No." := ItemJournalLineNo;
        ItemJournalLine."Journal Template Name" := 'MADDGNL';
        ItemJournalLine."Journal Batch Name" := 'SAYIM-1';
        ItemJournalLine.SetUpNewLine(LastItemJournalLine);

        ItemJournalLine.Insert(true);
        ItemJournalLine.Validate("Posting Date", WorkDate());
        ItemJournalLine.Validate("Item No.", PackageNoInfoLine."Line Item No.");
        ItemJournalLine.Validate("Variant Code", PackageNoInfoLine."Line Variant Code");
        ItemJournalLine.Validate("Location Code", PackageNoInfoLine."Location Code");
        ItemJournalLine.Validate("Lot No.", PackageNoInfoLine."Lot No.");
        ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Negative Adjmt.");
        ItemJournalLine.Validate(Quantity, Abs(PackageNoInfoLine.Quantity));
        ItemJournalLine.Modify(true);
    end;

    procedure DeletePackageFromHeader(var PackageNoInformation: Record "Package No. Information")
    var
        ConfirmManagement: Codeunit "Confirm Management";
    begin
        if ConfirmManagement.GetResponse('Selected lines will be deleted, are you sure ?') then
            repeat
                DeletePackNoLines(PackageNoInformation);
                PackageNoInformation.Delete(true);
            until PackageNoInformation.Next() = 0;
    end;

    // procedure DeletePackageFromLine(var PackageNoInfoLine: Record "Package No. Info. Line IPK")
    // var
    //     ConfirmManagement: Codeunit "Confirm Management";
    //     PackageNoInformation: Record "Package No. Information";
    // begin
    //     if ConfirmManagement.GetResponse('Selected lines will be deleted, are you sure ?') then
    //         repeat
    //             if PackageNoInformation.Get('', '', PackageNoInfoLine."Package No.") then
    //                 repeat
    //                     DeletePackNoLines(PackageNoInformation);
    //                     PackageNoInformation.Delete(true);
    //                 until PackageNoInformation.Next() = 0;
    //         until PackageNoInfoLine.Next() = 0;
    // end;

    procedure DeletePackageLine(var PackageNoInfoLine: Record "Package No. Info. Line IPK")
    var
        DeletedPackageNoInfoLines: Record "DeletedPackageNoInfoLines IPK";
        ConfirmManagement: Codeunit "Confirm Management";
    begin
        if ConfirmManagement.GetResponse('Selected lines will be deleted, are you sure ?') then
            repeat
                DeletedPackageNoInfoLines.Init();
                PackageNoInfoLine.CalcFields("Location Code");
                DeletedPackageNoInfoLines.TransferFields(PackageNoInfoLine);
                DeletedPackageNoInfoLines.Insert(true);
                PackageNoInfoLine.Delete(true);
            until PackageNoInfoLine.Next() = 0;
    end;

    procedure UpdateVoucherNos()
    var
        GLRegister: Record "G/L Register";
        LegalBooksFoundationSetupINF: Record "LegalBooksFoundation Setup INF";
        NoSeries: Codeunit "No. Series";
    begin
        LegalBooksFoundationSetupINF.Get();

        GLRegister.Reset();
        GLRegister.SetCurrentKey("Posting Date INF");
        GLRegister.SetFilter("Posting Date INF", '>=%1', 20250101D);
        if GLRegister.FindSet() then
            repeat
                GLRegister."Voucher No. INF" := NoSeries.GetNextNo(LegalBooksFoundationSetupINF."Voucher No. Series for GLReg", GLRegister."Posting Date INF", true);
                GLRegister.Modify(true);
            until GLRegister.Next() = 0;
    end;

    var
        EInvoiceSetup: Record "E-Invoice Setup INF";
}