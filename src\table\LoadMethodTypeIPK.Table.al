table 60002 "Load Method Type IPK"
{
    Caption = 'Load Method Type';
    DataClassification = CustomerContent;
    LookupPageId = "Load Method Types IPK";
    DrillDownPageId = "Load Method Types IPK";

    fields
    {
        field(1; "Load Method"; Enum "Load Method IPK")
        {
            Caption = 'Load Method';
            ToolTip = 'Specifies the value of the Load Method field.';
        }
        field(2; "Code"; Code[10])
        {
            Caption = 'Code';
            ToolTip = 'Specifies the value of the Code field.';
        }
    }
    keys
    {
        key(PK; "Load Method", "Code")
        {
            Clustered = true;
        }
    }
}