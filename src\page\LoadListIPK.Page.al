page 60000 "Load List IPK"
{
    ApplicationArea = All;
    Caption = 'Load List';
    PageType = List;
    SourceTable = "Load Header IPK";
    UsageCategory = Documents;
    Extensible = true;
    Editable = false;
    CardPageId = "Load Card IPK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("E-Shipment No."; Rec."E-Shipment No.")
                {
                }
                field("Seal No."; Rec."Seal No.")
                {
                }
                field("Container No."; Rec."Container No.")
                {
                }
                field("Ship-to Code"; Rec."Ship-to Code")
                {
                }
                field("Ship-to Name"; Rec."Ship-to Name")
                {
                }
                field("Warehouse Shipment No."; Rec."Warehouse Shipment No.")
                {
                    ToolTip = 'Specifies the value of the Warehouse Shipment No. field.';
                }
                field("Load Method"; Rec."Load Method")
                {
                }
                field("Load Method Type"; Rec."Load Method Type")
                {
                }
                field("Booking No."; Rec."Booking No.")
                {
                }
                field("Posted Document No."; Rec."Posted Document No.")
                {
                }
                field("Driver Name"; Rec."Driver Name")
                {
                }
                field("Driver Surname"; Rec."Driver Surname")
                {
                }
                field("Domestic Shipping Agent Code"; Rec."Domestic Shipping Agent Code")
                {
                }
                field("Truck License Plate"; Rec."Truck License Plate")
                {
                }
                field("Trailer License Plate"; Rec."Trailer License Plate")
                {
                }
                field("Partial Loading"; Rec."Partial Loading")
                {
                }
            }
        }
    }
}