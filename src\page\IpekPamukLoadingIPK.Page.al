page 60024 "Ipek Pamuk Loading IPK"
{
    ApplicationArea = All;
    Caption = 'Loading', Comment = 'Yükle';
    PageType = CardPart;
    SourceTable = "Ipek Pamuk Activity Cue IPK";
    RefreshOnActivate = true;
    layout
    {
        area(Content)
        {
            cuegroup(Loading)
            {
                Caption = 'Loading Cue Group';
                ShowCaption = false;
                field("Ready-to Load"; Rec."Ready-to Load")
                {
                }
                field("Ready-to Ship"; Rec."Ready-to Ship")
                {
                }
            }
            group(OpenLoadsGroup)
            {
                Caption = 'Open Loads';
                ShowCaption = false;

                cuegroup(OpenLoadsCueGroup)
                {
                    Caption = 'Open Loads Cue Group';
                    ShowCaption = false;

                    actions
                    {
                        action(OpenLoads)
                        {
                            Caption = 'Load', Comment = 'Yükleme';
                            Image = TileRed;
                            ToolTip = 'Executes the OpenLoads action.';
                            trigger OnAction()
                            var
                            begin
                                Page.Run(Page::"Load List IPK");
                            end;
                        }
                    }
                }
            }
        }
    }
}