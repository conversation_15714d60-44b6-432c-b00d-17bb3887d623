page 60002 "Whse. Receipt Line Details MXW"
{
    ApplicationArea = All;
    Caption = 'Warehouse Receipt Line Details';
    PageType = List;
    SourceTable = "Warehouse Receipt Line Dtl MXW";
    UsageCategory = Lists;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Item Tracking Info Assignd MXW"; Rec."Item Tracking Info Assignd MXW")
                {
                }
                field(Received; Re<PERSON><PERSON>Received)
                {
                }
            }
        }
    }
}
