pageextension 60040 "Sales Order List IPK" extends "Sales Order List"
{
    layout
    {
        modify("Ship-to Code")
        {
            Visible = true;
        }
        modify("Ship-to Name")
        {
            Visible = true;
        }

        moveafter("Sell-to Customer Name"; "Ship-to Code")
        moveafter("Ship-to Code"; "Ship-to Name")
        addafter("Ship-to Code")
        {
            field("Ship-to City IPK"; Rec."Ship-to City")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the city of the shipping address.';
            }
            // field("Requested Delivery Date IPK"; Rec."Requested Delivery Date IPK")
            // {
            //     ApplicationArea = All;
            // }
        }
    }
}