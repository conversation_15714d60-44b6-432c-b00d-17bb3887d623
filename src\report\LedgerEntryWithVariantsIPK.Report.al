report 60010 "Ledger Entry With Variants IPK"
{
    ApplicationArea = All;
    Caption = 'Item Ledger Entry Quantities With Variants';
    UsageCategory = ReportsAndAnalysis;
    // RDLCLayout = 'src/report/InventorybyLocation.rdlc';
    dataset
    {

        dataitem("Item Ledger Entry"; "Item Ledger Entry")
        {

            column(ItemNo_ItemLedgerEntry; "Item No.")
            {
            }
            column(VariantCode_ItemLedgerEntry; "Variant Code")
            {
            }
            column(LocationCode_ItemLedgerEntry; "Location Code")
            {
            }
            column(Quantity_ItemLedgerEntry; Quantity)
            {
            }
        }

    }


}