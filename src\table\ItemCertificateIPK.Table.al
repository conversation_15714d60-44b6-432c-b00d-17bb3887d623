table 60008 "Item Certificate IPK"
{
    Caption = 'Item Certificate';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Certificates IPK";
    LookupPageId = "Item Certificates IPK";


    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            NotBlank = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            begin
                Clear(NoSeries);

                if Rec."No." <> xRec."No." then begin
                    IpekPamukSetup.GetRecordOnce();
                    NoSeries.TestManual(IpekPamukSetup."Load No. Series");
                    Rec."No. Series" := '';
                end;
            end;
        }
        field(2; "Certificate Authority"; Code[10])
        {
            Caption = 'Certificate Authority';
            ToolTip = 'Specifies the value of the Certificate Authority field.';
        }
        field(3; "Item Type"; Code[10])
        {
            Caption = 'Item Type';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Item Type field.';
        }
        field(4; "Certificate ID"; Code[100])
        {
            Caption = 'Certificate ID';
            ToolTip = 'Specifies the value of the Certificate ID field.';
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }

    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    fieldgroups
    {
        fieldgroup(DropDown; "No.", "Certificate Authority", "Item Type", "Certificate ID", "Certificate ID") { }
    }
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        NoSeries: Codeunit "No. Series";

    trigger OnInsert()
    begin
        Clear(NoSeries);

        if Rec."No." = '' then begin
            IpekPamukSetup.GetRecordOnce();
            IpekPamukSetup.TestField("Certificate No. Series");
            Rec."No. Series" := IpekPamukSetup."Certificate No. Series";
            if NoSeries.AreRelated(IpekPamukSetup."Certificate No. Series", xRec."No. Series") then
                Rec."No. Series" := xRec."No. Series";

            Rec."No." := NoSeries.GetNextNo(IpekPamukSetup."Certificate No. Series");
        end;
    end;

    trigger OnDelete()
    var
        ItemCertificateLineIPK: Record "Item Certificate Line IPK";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmDeleteMsg: Label 'Do you want to delete the used certificates too ?';
    begin
        if ConfirmManagement.GetResponse(ConfirmDeleteMsg) then begin
            ItemCertificateLineIPK.SetRange("Certificate No.", Rec."No.");
            ItemCertificateLineIPK.DeleteAll(true);
        end;

    end;

}