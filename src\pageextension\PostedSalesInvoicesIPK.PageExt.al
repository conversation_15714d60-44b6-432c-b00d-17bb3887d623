pageextension 60059 "Posted Sales Invoices IPK" extends "Posted Sales Invoices"
{
    layout
    {
        addbefore("Bill-to Customer No.")
        {

            field("Export No. IPK"; Rec."Export No. IPK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addafter("&Invoice")
        {
            action("Update E-Document")
            {
                ApplicationArea = all;
                Caption = 'Update E-Document';
                Image = ReleaseDoc;
                ToolTip = 'Executes the Update E-Document action.';
                trigger OnAction()
                var
                    EInvoiceHeader: Record "E-Invoice Header INF";
                    IpekSalesManagement: Codeunit "Ipek Sales Management IPK";
                begin
                    EInvoiceHeader.SetRange("Document Type", EInvoiceHeader."Document Type"::"Sales Invoice");
                    EInvoiceHeader.SetRange("No.", Rec."No.");

                    if EInvoiceHeader.FindSet() then begin
                        EInvoiceHeader.Validate("Posting Date", Rec."Posting Date");
                        EInvoiceHeader.Validate("Issue Date", Rec."Posting Date");
                        EInvoiceHeader.Modify(true);

                        IpekSalesManagement.SetDocumentCurrencyCode(Rec, EInvoiceHeader);
                        Message('Document Updated !');
                    end;
                end;
            }
        }
    }
}