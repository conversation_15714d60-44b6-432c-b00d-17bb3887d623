pageextension 60004 "Sales Order Subform IPK" extends "Sales Order Subform"
{
    layout
    {
        addbefore("Unit Price")
        {

            field("Freight Inc PieceUnitPrice IPK"; Rec."Freight Inc PieceUnitPrice IPK")
            {
                ApplicationArea = All;
                DecimalPlaces = 0 : 2;
                ToolTip = 'Specifies the value of the Freight Included Piece Unit Price field.';
            }
            field("FOB Piece Unit Price IPK"; Rec."FOB Piece Unit Price IPK")
            {
                ApplicationArea = All;
                DecimalPlaces = 0 : 4;
                ShowMandatory = true;
            }
            field("Freight Inc Line Amount IPK"; Rec."FOB Line Amount IPK")
            {
                ApplicationArea = All;
            }

        }
        addbefore("Quantity")
        {
            field("Warehouse Qty. to Ship IPK"; Rec."Warehouse Qty. to Ship IPK")
            {
                ApplicationArea = All;
            }
        }
        modify("Unit Volume")
        {
            Visible = true;
        }
        modify("Net Weight")
        {
            Visible = true;
        }
        modify("Gross Weight")
        {
            Visible = true;
        }
        modify("Units per Parcel")
        {
            Visible = true;
        }
        addafter("Units per Parcel")
        {
            field("Line Net Weight IPK"; Rec."Line Net Weight IPK")
            {
                ApplicationArea = All;
            }
            field("Line Unit Quantity IPK"; Rec."Line Unit Quantity IPK")
            {
                ApplicationArea = All;
            }
            field("Line Gross Weight IPK"; Rec."Line Gross Weight IPK")
            {
                ApplicationArea = All;
            }
            field("Line Volume IPK"; Rec."Line Volume IPK")
            {
                ApplicationArea = All;
            }
        }
        modify(Quantity)
        {
            trigger OnAfterValidate()
            begin


                Rec.Validate("Warehouse Qty. to Ship IPK", 0);



                CurrPage.Update();
            end;
        }
        modify("Qty. to Assemble to Order")
        {
            Visible = false;
        }
    }
}