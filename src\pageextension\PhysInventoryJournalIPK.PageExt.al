pageextension 60068 "Phys. Inventory Journal IPK" extends "Phys. Inventory Journal"
{
    layout
    {
        addafter("Qty. (Phys. Inventory)")
        {

            // field("Difference Positive IPK"; Rec."Difference Positive IPK")
            // {
            //     ApplicationArea = All;
            // }
            // field("Difference Negative IPK"; Rec."Difference Negative IPK")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Difference field.';
            // }
        }
    }
    actions
    {
        addafter(CalculateInventory)
        {
            action("Adjust To Zero IPK")
            {
                ApplicationArea = All;
                Caption = 'Caption', Comment = 'TRK="YourLanguageCaption"';
                Image = AdjustEntries;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Caption action.';

                trigger OnAction()
                var
                    ItemJournalLine: Record "Item Journal Line";
                    TmpItemTrackLineInsert: Record "Tracking Specification";
                    TmpSourceTrackingSpecification: Record "Tracking Specification";
                    TmpTrackingSpecification: Record "Tracking Specification";
                    ItemJnlLineReserve: Codeunit "Item Jnl. Line-Reserve";
                    ItemTrackingDataCollection: Codeunit "Item Tracking Data Collection";
                begin
                    ItemJournalLine.SetRange("Journal Batch Name", Rec."Journal Batch Name");
                    ItemJournalLine.SetRange("Journal Template Name", Rec."Journal Template Name");
                    ItemJournalLine.FindSet();

                    repeat
                        ItemJournalLine.Validate("Qty. (Phys. Inventory)", 0);
                        ItemJournalLine.Modify(true);
                    until ItemJournalLine.Next() = 0;

                    ItemJournalLine.Reset();
                    ItemJournalLine.SetRange("Journal Batch Name", Rec."Journal Batch Name");
                    ItemJournalLine.SetRange("Journal Template Name", Rec."Journal Template Name");
                    ItemJournalLine.FindSet();


                    repeat

                        ItemJnlLineReserve.InitFromItemJnlLine(TmpSourceTrackingSpecification, Rec);
                        TmpTrackingSpecification.Init();
                        TmpTrackingSpecification.Insert(false);

                        ItemTrackingDataCollection.RetrieveLookupData(TmpSourceTrackingSpecification, true);
                        repeat
                            TmpItemTrackLineInsert.TransferFields(TmpSourceTrackingSpecification);
                            TmpItemTrackLineInsert.Insert(false);
                        until TmpSourceTrackingSpecification.Next() = 0;
                    until ItemJournalLine.Next() = 0;

                end;
            }
            action("assign Lot No IPK")
            {
                ApplicationArea = All;
                Caption = 'assign Lot No', Comment = 'TRK="YourLanguageCaption"';
                Image = Lot;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the assign Lot No action.';

                trigger OnAction()
                begin

                    // ItemJournalLine.SetRange("Journal Batch Name", Rec."Journal Batch Name");
                    // ItemJournalLine.SetRange("Journal Template Name", Rec."Journal Template Name");
                    // ItemJournalLine.FindSet();


                    //repeat

                    InventoryAdjustment.AssignLotNoToJrnlLine(Rec);
                    //until ItemJournalLine.Next() = 0;

                end;
            }
        }
    }


    var
        InventoryAdjustment: Codeunit "Inventory Adjustment IPK";
}