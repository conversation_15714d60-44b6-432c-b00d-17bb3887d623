tableextension 60041 "Posted Whse. Receipt Line IPK" extends "Posted Whse. Receipt Line"
{
    fields
    {
        field(60000; "Vendor Shipment No. IPK"; Code[50])
        {
            Caption = 'Vendor Shipment No.';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("Posted Whse. Receipt Header"."Vendor Shipment No." where("No." = field("No.")));
            ToolTip = 'Specifies the value of the Vendor Shipment No. field.';
        }
        field(60001; "Vendor No. IPK"; Code[20])
        {
            Caption = 'Vendor No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Posted Whse. Receipt Header"."Vendor No. IPK" where("No." = field("No.")));
            ToolTip = 'Specifies the value of the Vendor Shipment No. field.';
        }
        field(60002; "Unit Weight IPK"; Decimal)
        {
            Caption = 'Unit Weight';
            Editable = false;
            FieldClass = FlowField;
            AllowInCustomizations = Always;
            CalcFormula = lookup("Item Unit of Measure".Weight where("Item No." = field("Item No.")));
        }

    }
}