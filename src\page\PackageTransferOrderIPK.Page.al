page 60018 "Package Transfer Order IPK"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Order';
    PageType = Document;
    SourceTable = "Package Transfer Header IPK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus IPK")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                end;
            }
            group(General)
            {
                Caption = 'General';
                Editable = not Rec.Received or not Rec.Shipped;
                field("No."; Rec."No.")
                {
                    QuickEntry = false;
                }
                field("Package Transfer Type"; Rec."Package Transfer Type")
                {
                }
                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                    Editable = false;
                }
                // field("Location Filter"; Rec."Location Filter")
                // {
                //     //Visible = false;
                //     //Importance = Additional;
                // }

                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    QuickEntry = false;
                    ToolTip = 'Specifies the value of the Transfer-to Code field.';
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;

                    // trigger OnLookup(var Text: Text): Boolean
                    // var
                    //     Location: Record Location;
                    // begin
                    //     Location.SetFilter(Code, Rec."Location Filter");

                    //     if Page.RunModal(0, Location) = Action::LookupOK then
                    //         Rec.Validate("Transfer-to Code", Location.Code);
                    // end;
                }

                // field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                // {
                //     QuickEntry = false;
                // }
                field("Posting Date"; Rec."Posting Date")
                {
                    QuickEntry = false;
                }
                field(Shipped; Rec.Shipped)
                {
                    Editable = false;
                }
                // field("Production Order No.1"; Rec."Production Order No.")
                // {
                //     QuickEntry = false;
                //     Visible = TransferringToProdOrder;
                //     ShowMandatory = TransferringToProdOrder;
                //     trigger OnLookup(var Text: Text): Boolean
                //     var
                //         ProductionOrder: Record "Production Order";
                //     begin
                //         ProductionOrder.SetFilter("No.", Rec."Location Filter");

                //         if Page.RunModal(Page::"Released Production Orders", ProductionOrder) = Action::LookupOK then
                //             Rec.Validate("Production Order No.", ProductionOrder."No.");
                //     end;
                // }
                field(Posted; Rec.Received)
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                }
                field("Is Production Location"; Rec."Is Production Location")
                {
                }

            }
            // group(Production)
            // {
            //     Caption = 'Production';
            //     field("Transferring to Prod. Location"; Rec."Transferring to Prod. Location")
            //     {
            //         QuickEntry = false;
            //     }
            //     field("Production Order No."; Rec."Production Order No.")
            //     {
            //         QuickEntry = false;
            //         ShowMandatory = TransferringToProdOrder;
            //         trigger OnLookup(var Text: Text): Boolean
            //         var
            //             ProductionOrder: Record "Production Order";
            //         begin
            //             ProductionOrder.SetFilter("No.", Rec."Location Filter");

            //             if Page.RunModal(Page::"Released Production Orders", ProductionOrder) = Action::LookupOK then
            //                 Rec.Validate("Production Order No.", ProductionOrder."No.");
            //         end;
            //     }
            // }
            group(BarcodeReadingArea)
            {
                Caption = 'Barcode Reading';
                Editable = not Rec.Received or not Rec.Shipped;

                field(Barcode; Rec.Barcode)
                {
                    Editable = (Rec."No." <> '');
                    QuickEntry = true;
                    trigger OnValidate()
                    begin

                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                    end;
                }
            }
            part(Lines; "Package Transfer Subpage IPK")
            {
                Caption = 'Lines';
                Editable = not Rec.Received or not Rec.Shipped;
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                //todo:sort by lineno desc

            }

        }

    }
    actions
    {
        area(Processing)
        {
            action(ShipAndReceive)
            {
                Caption = 'Ship & Receive';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Post;
                ToolTip = 'Executes the Ship & Receive action.';
                Enabled = not Rec.Received;
                PromotedOnly = true;
                Visible = not Rec."Is Production Location";

                trigger OnAction()
                var
                    QualityControlHeaderQCM: Record "Quality Control Header QCM";
                begin


                    IpekPackageTransMgt.ShipAndRecievePackagetransferHeader(Rec, QualityControlHeaderQCM);
                end;
            }
            action(Ship)
            {
                Caption = 'Ship';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Shipment;
                ToolTip = 'Executes the Ship action.';
                Enabled = not Rec.Shipped and not Rec.Received;
                Visible = Rec."Is Production Location";
                PromotedOnly = true;

                trigger OnAction()
                begin
                    Rec.Validate(Shipped, true);
                end;
            }
            action(Recive)
            {
                Caption = 'Recive';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReceiveLoaner;
                ToolTip = 'Executes the Recive action.';
                Enabled = Rec.Shipped and not Rec.Received;
                Visible = Rec."Is Production Location";
                PromotedOnly = true;

                trigger OnAction()
                begin
                    Rec.TestField(Shipped, true);
                    PackageTransferLine.SetRange("Document No.", Rec."No.");
                    PackageTransferLine.FindSet();
                    repeat
                        if not (PackageTransferLine."Quantity To Transfer" > 0) then
                            Error(AtleastErr, PackageTransferLine."Item No.");
                    until PackageTransferLine.Next() = 0;
                    IpekPackageTransMgt.CreateAndPostItemReclassificationJournal(Rec, false);
                end;



            }
            action(Switch)
            {
                Caption = 'Reverse Transfer';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReceiveLoaner;
                ToolTip = 'Executes the Recive action.';
                // Enabled = Rec.Shipped and not Rec.Received;
                Visible = false;
                PromotedOnly = true;

                trigger OnAction()
                var
                    TransferfromCode: Code[10];
                    TransfertoCode: Code[10];
                begin
                    TransfertoCode := Rec."Transfer-to Code";
                    TransferfromCode := Rec."Transfer-from Code";
                    Rec."Transfer-to Code" := TransferfromCode;
                    Rec."Transfer-from Code" := TransfertoCode;
                    Rec.Shipped := false;
                    Rec.Received := false;
                end;

            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        // Rec.CalcFields("Transferring to Prod. Location");
        // TransferringToProdOrder := Rec."Transferring to Prod. Location";
    end;



    var
        PackageTransferLine: Record "Package Transfer Line IPK";
        IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";
        AtleastErr: Label 'You have to transfer at least one units of: %1', Comment = '%1="Item Journal Line"."Item No."';
    // TransferringToProdOrder: Boolean;
}